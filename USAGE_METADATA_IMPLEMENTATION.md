# Usage Metadata and Cost Tracking Implementation

## Overview

This implementation adds comprehensive usage metadata tracking and cost analysis to the TTS (Text-to-Speech) API responses, along with dedicated cost tracking endpoints for monitoring and analyzing API usage costs.

## Features Implemented

### 1. Enhanced TTS Response with Usage Metadata

**File:** `app/v2/gemini_tts.py`

#### New Response Structure
The `GeminiTTSResponse` now includes detailed usage metadata:

```json
{
  "message": "Audio generated successfully",
  "file_id": "unique-file-id",
  "file_name": "generated_audio.wav",
  "minio_path": "path/to/audio/file",
  "presigned_url": "https://minio-url/...",
  "text": "Input text for TTS",
  "voice_name": "<PERSON>oe<PERSON>",
  "created_at": "2025-07-02T10:30:00Z",
  "usage_metadata": {
    "prompt_token_count": 25,
    "candidates_token_count": 0,
    "total_token_count": 25,
    "estimated_cost_usd": 0.003125,
    "estimated_cost_npr": 0.4281
  }
}
```

#### Key Changes
- **UsageMetadata Model**: New Pydantic model for structured usage data
- **Cost Calculation**: Automatic cost estimation in both USD and NPR
- **Database Storage**: Usage metadata stored in MongoDB for historical tracking
- **API Response**: Real-time usage and cost information in TTS responses
- **Default Style Instructions**: Fixed style instructions applied automatically

#### Cost Calculation Logic
- Based on Google Gemini TTS pricing: $0.000125 per 1000 input characters
- Converts to Nepali Rupees using 1 USD = 137 NPR exchange rate
- Calculates cost using input text length (character count)
- Provides 6-decimal precision for USD and 4-decimal precision for NPR

### 2. Comprehensive Cost Tracking API

**File:** `app/v2/cost_tracking.py`

#### Endpoints

##### `/v2/cost-tracking/summary`
Provides simplified cost summary for TTS usage:

**Query Parameters:**
- `start_date` (optional): Start date in YYYY-MM-DD format
- `end_date` (optional): End date in YYYY-MM-DD format

**Response Features:**
- Total cost in both USD and NPR
- Total requests and tokens
- Average cost per request in both currencies
- Date range information
- Simplified summary focused on TTS costs only

### 3. Database Schema Updates

#### TTS Collection Enhancement
Each TTS record now includes:
```json
{
  "file_id": "unique-id",
  "file_name": "audio.wav",
  "minio_path": "storage/path",
  "text": "input text",
  "voice_name": "Aoede",
  "style_instructions": "Speak the following with no preamble or explanation",
  "user_id": "user-id",
  "created_at": "2025-07-02T10:30:00Z",
  "file_size": 1024000,
  "usage_metadata": {
    "prompt_token_count": 25,
    "candidates_token_count": 0,
    "total_token_count": 25,
    "estimated_cost_usd": 0.003125,
    "estimated_cost_npr": 0.4281
  }
}
```

### 4. Integration with Existing Architecture

#### Router Integration
- Cost tracking router added to main v2 FastAPI application
- Maintains existing authentication patterns using `get_tenant_info`
- Follows tenant-based database architecture
- Compatible with existing MongoDB connection patterns

#### Authentication
- All endpoints require Bearer token authentication
- Tenant-based access control maintained
- User context preserved for cost attribution

## Usage Examples

### 1. Generate TTS with Usage Metadata
```bash
curl -X POST "http://localhost:8000/v2/gemini-tts/tts" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "text=Hello world&voice_name=Aoede"
```

### 2. Get Cost Summary
```bash
curl -X GET "http://localhost:8000/v2/cost-tracking/summary?start_date=2025-07-01&end_date=2025-07-02" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Testing

A comprehensive test script is provided in `test_usage_metadata.py` that validates:
- TTS response includes usage metadata with both USD and NPR pricing
- Cost tracking summary endpoint functionality
- Error handling and authentication

## Technical Implementation Details

### Cost Calculation Algorithm
1. **Input Processing**: Extract text length in characters
2. **Rate Application**: Apply Gemini TTS pricing ($0.000125 per 1K chars)
3. **Precision**: Round to 6 decimal places for billing accuracy
4. **Storage**: Store calculated cost in database for historical analysis

### Usage Metadata Capture
1. **API Response Parsing**: Extract usage_metadata from Gemini API response
2. **Token Counting**: Capture prompt, candidates, and total token counts
3. **Real-time Processing**: Calculate costs during TTS generation
4. **Database Persistence**: Store all metadata for future analysis

### Performance Considerations
- Minimal overhead added to TTS generation process
- Efficient database queries with proper indexing on created_at and user_id
- Pagination support for large datasets
- Async/await patterns maintained throughout

## Future Enhancements

1. **Cost Alerts**: Implement usage threshold notifications
2. **Billing Integration**: Connect to payment processing systems
3. **Advanced Analytics**: Add trend analysis and forecasting
4. **Export Features**: CSV/Excel export for cost reports
5. **Real-time Dashboards**: WebSocket-based live cost monitoring

## Dependencies

- FastAPI for API framework
- Pydantic for data validation
- MongoDB for data persistence
- Google Gemini API for TTS services
- Existing authentication and database infrastructure
