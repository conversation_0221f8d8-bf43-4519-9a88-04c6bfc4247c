from fastapi import APIRouter, UploadFile, File, Form, Depends, HTTPException
from fastapi.responses import JSONResponse
from elevenlabs import ElevenLabs
from io import BytesIO
from datetime import datetime, timezone, timedelta
from typing import List, Optional
from pydantic import BaseModel
import logging
import uuid
import os

# Custom imports
from core.security import get_tenant_info
from core.database import get_async_db_from_tenant_id

# Response models
class VoiceInfo(BaseModel):
    voice_id: str
    name: str
    category: str

class VoiceListResponse(BaseModel):
    message: str
    total_voices: int
    voices: List[VoiceInfo]

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/voice-clone", tags=["Voice Cloning"])


@router.get("/list-voices", response_model=VoiceListResponse)
async def list_voices_from_elevenlabs(
    user_tenant_info = Depends(get_tenant_info)
):
    """
    List custom (non-default) voices from ElevenLabs API using search endpoint
    """
    logger.info("Starting request to list custom voices from ElevenLabs API")

    try:
        # Get ElevenLabs API key from tenant configuration
        logger.debug("Fetching ElevenLabs API key from tenant DB")
        api_key_doc = user_tenant_info.db.config.find_one({"name": "api-keys"})

        if not api_key_doc or "ELEVEN_LABS_KEY" not in api_key_doc.get("value", {}):
            logger.error("ElevenLabs API key not found in tenant configuration")
            raise HTTPException(status_code=500, detail="ElevenLabs API key not configured")

        api_key = api_key_doc["value"]["ELEVEN_LABS_KEY"]
        logger.debug(f"API key retrieved (length: {len(api_key)})")

        # Initialize ElevenLabs client
        client = ElevenLabs(api_key=api_key)
        logger.debug("ElevenLabs client initialized")

        # Search for custom (non-default) voices from ElevenLabs API
        logger.info("Searching for custom voices from ElevenLabs API")
        try:
            voices_response = client.voices.search(
                voice_type="non-default",
                include_total_count=True,
                page_size=100  # Set to maximum to get all voices in one request
            )
            logger.debug(f"Successfully retrieved custom voices from ElevenLabs API")
        except Exception as e:
            logger.error(f"Failed to search voices from ElevenLabs API: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to search voices from ElevenLabs: {str(e)}")

        # Process voices data
        voices_list = []
        for voice in voices_response.voices:
            voice_info = VoiceInfo(
                voice_id=voice.voice_id,
                name=voice.name,
                category=getattr(voice, 'category', 'unknown')
            )
            voices_list.append(voice_info)

        # Use total_count from search response if available, otherwise use length of voices list
        total_count = getattr(voices_response, 'total_count', len(voices_list))

        logger.info(f"Successfully processed {len(voices_list)} custom voices from ElevenLabs (total: {total_count})")

        # Return response
        return VoiceListResponse(
            message="Custom voices retrieved successfully from ElevenLabs API",
            total_voices=total_count,
            voices=voices_list
        )

    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        logger.exception(f"Unexpected error listing custom voices: {str(e)}")
        raise HTTPException(status_code=500, detail="An error occurred while listing custom voices")


@router.post("/")
async def clone_voice(
    user_name: str = Form(...),  # Voice name from request body
    user_id: str = Form(...),
    files: List[UploadFile] = File(...),
    user_tenant_info = Depends(get_tenant_info)
):
    logger.info("Starting voice cloning request")
    try:
        logger.debug("Fetching ElevenLabs API key from tenant DB")
        api_key_doc = user_tenant_info.db.config.find_one({"name": "api-keys"})
        if not api_key_doc or "ELEVEN_LABS_KEY" not in api_key_doc.get("value", {}):
            logger.error("API key not found in tenant configuration")
            raise HTTPException(status_code=500, detail="API key missing")

        api_key = api_key_doc["value"]["ELEVEN_LABS_KEY"]
        client = ElevenLabs(api_key=api_key)
        logger.debug("ElevenLabs client initialized")

        logger.debug("Getting tenant-specific database connection")
        tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)

        # Check if the voice with the same name already exists for this user
        existing_voice = await tenant_database.cloned_voices.find_one({
            "name": user_name,
            "user_id": user_id
        })

        if existing_voice:
            logger.warning(f"Voice '{user_name}' already exists for user_id '{user_id}'")
            raise HTTPException(
                status_code=409,
                detail=f"Voice '{user_name}' already exists for this user"
            )

        # Prepare audio files and save to MinIO
        audio_samples = []
        saved_files = []

        logger.info(f"💾 Saving {len(files)} audio files to MinIO organized by user_id: {user_id}")

        try:
            # Get MinIO client
            minio_client = user_tenant_info.minio_client
            bucket_name = user_tenant_info.minio_bucket_name

            for i, file in enumerate(files):
                contents = await file.read()
                audio_samples.append(BytesIO(contents))

                # Generate unique filename for each audio file
                file_id = str(uuid.uuid4())
                file_extension = os.path.splitext(file.filename)[1] if file.filename else '.wav'
                file_name = f"voice_sample_{i+1}_{file_id}{file_extension}"

                # Create user-specific path: voice_cloning/{user_id}/{voice_name}/
                minio_path = f"voice_cloning/{user_id}/{user_name}/{file_name}"

                # Upload to MinIO
                audio_buffer = BytesIO(contents)
                minio_client.put_object(
                    bucket_name=bucket_name,
                    object_name=minio_path,
                    data=audio_buffer,
                    length=len(contents),
                    content_type=file.content_type or "audio/wav"
                )

                # Generate presigned URL for direct access (valid for 24 hours)
                try:
                    presigned_url = minio_client.presigned_get_object(
                        bucket_name=bucket_name,
                        object_name=minio_path,
                        expires=timedelta(hours=24)
                    )
                    logger.debug(f"🔗 Generated presigned URL for {file_name}")
                except Exception as e:
                    logger.error(f"❌ Failed to generate presigned URL for {file_name}: {str(e)}")
                    presigned_url = f"Error generating URL: {str(e)}"

                saved_files.append({
                    "file_id": file_id,
                    "original_filename": file.filename,
                    "file_name": file_name,
                    "minio_path": minio_path,
                    "file_size": len(contents),
                    "content_type": file.content_type or "audio/wav",
                    "presigned_url": presigned_url
                })

                logger.debug(f"📁 Saved audio file {i+1}/{len(files)}: {minio_path}")

            logger.info(f"✅ All {len(files)} audio files saved to MinIO successfully")

        except Exception as e:
            logger.error(f"❌ Failed to save audio files to MinIO: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to save audio files: {str(e)}")

        # Check voice quota before creating new voice
        logger.info("🔍 Checking voice quota before cloning")
        try:
            voice_search_response = client.voices.search(voice_type="non-default", include_total_count=True)
            current_custom_voices = voice_search_response.total_count
            logger.info(f"📊 Current custom voices: {current_custom_voices}/30")

            if current_custom_voices >= 30:
                logger.error(f"❌ Voice quota exceeded: {current_custom_voices}/30 custom voices")
                raise HTTPException(
                    status_code=429,
                    detail=f"Voice cloning quota exceeded. You have {current_custom_voices}/30 custom voices. Please delete some voices before creating new ones."
                )
        except HTTPException:
            raise  # Re-raise quota exception
        except Exception as e:
            logger.warning(f"⚠️ Could not check voice quota: {str(e)}. Proceeding with voice creation.")

        logger.info(f"Cloning voice: {user_name}")
        voice = client.voices.ivc.create(
            name=user_name,
            files=audio_samples
        )
        logger.info(f"✅ Voice cloned with ID: {voice.voice_id}")

        logger.debug("📝 Storing cloned voice metadata in tenant DB")
        voice_doc = {
            "voice_id": voice.voice_id,
            "user_id": user_id,
            "name": user_name,
            "username": user_tenant_info.user.username,
            "created_at": datetime.now(timezone.utc),
            "audio_files": saved_files,
            "total_files": len(saved_files),
            "minio_base_path": f"voice_cloning/{user_id}/{user_name}/"
        }

        await tenant_database.cloned_voices.insert_one(voice_doc)

        logger.info("🎉 Voice cloning process completed successfully")
        return JSONResponse(
            content={
                "voice_id": voice.voice_id,
                "message": f"Voice '{user_name}' cloned successfully",
                "user_id": user_id,
                "voice_name": user_name,
                "files_saved": len(saved_files),
                "minio_base_path": f"voice_cloning/{user_id}/{user_name}/",
                "audio_files": [
                    {
                        "file_name": file_info["file_name"],
                        "minio_path": file_info["minio_path"],
                        "file_size": file_info["file_size"],
                        "presigned_url": file_info["presigned_url"]
                    } for file_info in saved_files
                ]
            },
            status_code=200
        )

    except HTTPException:
        raise  # Let FastAPI handle HTTPExceptions as is

    except Exception as e:
        logger.exception(f"Voice cloning error: {str(e)}")  # Includes traceback
        raise HTTPException(status_code=500, detail="An error occurred during voice cloning")



@router.delete("/delete/{voice_id}")
async def delete_cloned_voice(
    voice_id: str,
    user_tenant_info = Depends(get_tenant_info)
):
    """
    Delete a cloned voice by voice_id from both database and ElevenLabs
    """
    logger.info(f" Starting deletion of cloned voice with voice_id: {voice_id}")

    try:
        # Get tenant database
        tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)

        # Find the voice to delete
        voice_doc = await tenant_database.cloned_voices.find_one({"voice_id": voice_id})

        if not voice_doc:
            logger.error(f"Voice with voice_id '{voice_id}' not found")
            raise HTTPException(status_code=404, detail=f"Voice with ID '{voice_id}' not found")

        voice_name = voice_doc["name"]
        user_id = voice_doc["user_id"]
        audio_files = voice_doc.get("audio_files", [])

        logger.info(f"📋 Found voice to delete: {voice_name} (user_id: {user_id})")

        # Delete from ElevenLabs first
        try:
            logger.info("Deleting voice from ElevenLabs")

            # Get ElevenLabs API key
            api_key_doc = user_tenant_info.db.config.find_one({"name": "api-keys"})
            if not api_key_doc or "ELEVEN_LABS_KEY" not in api_key_doc.get("value", {}):
                logger.error(" ElevenLabs API key not found")
                raise HTTPException(status_code=500, detail="ElevenLabs API key not configured")

            api_key = api_key_doc["value"]["ELEVEN_LABS_KEY"]
            client = ElevenLabs(api_key=api_key)

            # Delete from ElevenLabs
            client.voices.delete(voice_id)
            logger.info(f" Voice deleted from ElevenLabs: {voice_id}")
            elevenlabs_deleted = True

        except Exception as e:
            logger.error(f" Failed to delete voice from ElevenLabs: {str(e)}")
            # Continue with database deletion even if ElevenLabs deletion fails
            elevenlabs_deleted = False

        # Delete audio files from MinIO
        minio_client = user_tenant_info.minio_client
        bucket_name = user_tenant_info.minio_bucket_name
        deleted_files = []
        failed_deletions = []

        logger.info(f" Deleting {len(audio_files)} audio files from MinIO")

        for file_info in audio_files:
            try:
                minio_path = file_info["minio_path"]
                file_name = file_info["file_name"]

                # Delete file from MinIO
                minio_client.remove_object(bucket_name, minio_path)
                deleted_files.append(file_name)
                logger.debug(f"Deleted file: {file_name}")

            except Exception as e:
                logger.error(f" Failed to delete file {file_info.get('file_name')}: {str(e)}")
                failed_deletions.append(file_info.get("file_name", "unknown"))

        # Delete voice document from database
        logger.info(" Deleting voice from database")
        delete_result = await tenant_database.cloned_voices.delete_one({"voice_id": voice_id})

        if delete_result.deleted_count == 0:
            logger.error(" Failed to delete voice from database")
            raise HTTPException(status_code=500, detail="Failed to delete voice from database")

        logger.info(f" Voice deletion completed: {voice_name}")

        return JSONResponse(
            content={
                "voice_id": voice_id,
                "voice_name": voice_name,
                "user_id": user_id,
                "message": f"Voice '{voice_name}' deleted successfully",
                "elevenlabs_deleted": elevenlabs_deleted,
                "files_deleted": len(deleted_files),
                "files_failed": len(failed_deletions),
                "deleted_files": deleted_files,
                "failed_deletions": failed_deletions if failed_deletions else None
            },
            status_code=200
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f" Error deleting voice: {str(e)}")
        raise HTTPException(status_code=500, detail="An error occurred while deleting the voice")

