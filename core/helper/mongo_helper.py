from bson import ObjectId
from datetime import datetime

def convert_objectid_to_str(document):
    if isinstance(document, dict):
        for key, value in document.items():
            if isinstance(value, ObjectId):
                document[key] = str(value)
            elif isinstance(value, dict):
                convert_objectid_to_str(value)
            elif isinstance(value, list):
                for index, item in enumerate(value):
                    if isinstance(item, ObjectId):
                        value[index] = str(item)
                    else:
                        convert_objectid_to_str(item)
    elif isinstance(document, list):
        for index, item in enumerate(document):
            if isinstance(item, ObjectId):
                document[index] = str(item)
            else:
                convert_objectid_to_str(item)

    return document


def serialize_mongo_doc(doc):
    """Serialize MongoDB document for JSON response"""
    if isinstance(doc, dict):
        return {k: serialize_mongo_doc(v) for k, v in doc.items()}
    elif isinstance(doc, list):
        return [serialize_mongo_doc(item) for item in doc]
    elif isinstance(doc, ObjectId):
        return str(doc)
    elif isinstance(doc, datetime):
        return doc.isoformat()
    return doc