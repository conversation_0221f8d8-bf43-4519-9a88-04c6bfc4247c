from pymongo import MongoClient, AsyncMongoClient
from dotenv import load_dotenv
from bson import ObjectId
import os
from fastapi import HTTPException
load_dotenv()

def get_admin_db():
    ADMIN_DB_NAME = "aroma2_admin"
    try:
        return MongoClient(os.getenv("MONGO_URI"))[ADMIN_DB_NAME] 
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Admin database not found\nError:{e}")

def get_db_from_tenant_id(tenant_id:str):
    # print(tenant_id)
    try:
        tenant_database_name = get_admin_db().tenants.find_one({"_id": ObjectId(tenant_id)})["db_name"]
        # return tenant_database_name
        return MongoClient(os.getenv("MONGO_URI"))[tenant_database_name]
    except Exception as e:
        raise HTTPException(status_code=401, detail=f"Invalid tenant credentials\nError:{e}")
        
def get_tenant_id_and_name_from_slug(slug:str):
    try:
        tenant_id_and_name = get_admin_db().tenants.find_one({"slug":slug},{"_id": 1, "name": 1})
        if not tenant_id_and_name:
            raise Exception("Slug not found")
        return tenant_id_and_name
    except Exception as e:
        print("raised an error")
        raise HTTPException(status_code=401, detail=f"Invalid slug credentials\nError{e}")

def get_async_db_from_tenant_id(tenant_id: str):
    try:
        tenant_database_name = get_admin_db().tenants.find_one({"_id": ObjectId(tenant_id)})["db_name"]
        return AsyncMongoClient(os.getenv("MONGO_URI"))[tenant_database_name]
    except Exception as e:
        raise HTTPException(status_code=401, detail=f"Invalid tenant credentials\nError:{e}")
    
