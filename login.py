from core.helper.mongo_helper import convert_objectid_to_str
from datetime import timedelta
from models.user import UserTenantDB
from datetime import datetime, timedelta
loggers = logger.setup_new_logging(__name__)

router = APIRouter(tags=["Users"])

from .user_details import router as user_details_router
router.include_router(user_details_router)

@router.get("/get_tenant_id")
async def tenantid_from_slug(slug: str):
    try:
        result = get_tenant_id_and_name_from_slug(slug)
        tenant_id = str(result["_id"])
        tenant_name = result["name"]

        return{
            "tenant_id":tenant_id,
            "tenant_name":tenant_name
        }
    except Exception:
        raise Exception

@router.post("/login")
async def login(form_data: OAuth2PasswordRequestFormWithClientID = Depends()):
    # find the database name of that tenant
    result = get_tenant_id_and_name_from_slug(form_data.client_id)

    tenant_id = str(result["_id"])
    # tenant_database = get_db_from_tenant_id(tenant_id)
    tenant_database = get_async_db_from_tenant_id(tenant_id)

    # print(tenant_database)
    # connect to the user_collection of that database.
    user = await tenant_database.users.find_one({"username": form_data.username})

    if not user :
        loggers.error(f"User not found: {form_data.username}")
        raise HTTPException(status_code=401, detail="User not found")

    if not verify_password(form_data.password, user["hashed_password"]):
        loggers.error(f"Incorrect Credentials: {form_data.username}")
        raise HTTPException(status_code=401, detail="Incorrect Credentials")

    access_token = create_access_token(
        data={"sub": user["username"], "role": user["role"], "tenant_id": tenant_id},
        expires_delta=timedelta(days=1)
    )


    user = convert_objectid_to_str(user)

    # print(result)
    loggers.info(
        msg=f"Tenant Database: {tenant_database}\tUser: {user}"
    )

    return {
        "id": user["_id"],
        "access_token": access_token,
        "token_type": "bearer",
        "username": user['username'],
        "role": user['role'],
        "tenant_id": tenant_id,
        "tenant_label": result["name"],
        "tenant_slug": form_data.client_id,
    }





@router.get("/verify_token")
async def verify_token(user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
    """If the token is valid, return the user's details
       If the token is invalid, get_current_user will raise an exception
    """
    return {"valid": True, "user": user_tenant_info.user.model_dump()}

