from fastapi import APIRouter, Depends, HTTPException, Form
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from elevenlabs import ElevenLabs
from io import BytesIO
from datetime import datetime, timezone, timedelta
import logging
import uuid

# Custom imports
from core.security import get_tenant_info
from core.database import get_async_db_from_tenant_id

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/tts", tags=["TTS"])

class TTSResponse(BaseModel):
    message: str
    file_id: str
    file_name: str
    minio_path: str
    presigned_url: str
    text: str
    voice_name: str
    created_at: datetime

@router.post("/speak", response_model=TTSResponse)
async def speak(
    text: str = Form(..., description="Text to convert to speech"),
    voice_id: str = Form(..., description="ElevenLabs voice ID to use"),
    user_tenant_info = Depends(get_tenant_info)
):
    logger.info("🎵 Starting TTS generation request")
    logger.debug(f"📝 Request parameters - text length: {len(text)}, voice_id: {voice_id}")
    logger.debug(f"👤 User info - tenant_id: {user_tenant_info.tenant_id}, username: {user_tenant_info.user.username}")

    try:
        # Fetch ElevenLabs API key from tenant DB
        logger.debug(" Fetching ElevenLabs API key from tenant DB")
        logger.debug(f"Accessing tenant database for tenant_id: {user_tenant_info.tenant_id}")

        api_key_doc = user_tenant_info.db.config.find_one({"name": "api-keys"})
        logger.debug(f" API key document found: {api_key_doc is not None}")

        if not api_key_doc or "ELEVEN_LABS_KEY" not in api_key_doc.get("value", {}):
            logger.error(" API key not found in tenant configuration")
            logger.debug(f"Available config keys: {list(api_key_doc.get('value', {}).keys()) if api_key_doc else 'None'}")
            raise HTTPException(status_code=500, detail="API key missing")

        api_key = api_key_doc["value"]["ELEVEN_LABS_KEY"]
        logger.debug(f" API key retrieved (length: {len(api_key)})")

        client = ElevenLabs(api_key=api_key)
        logger.debug(" ElevenLabs client initialized")

        # Use the provided voice_id directly
        logger.info(f" Using provided voice_id: {voice_id}")
        logger.debug(f" Validating voice_id format: {len(voice_id)} characters")

        # Optional: Get voice name from database for metadata (if it's a cloned voice)
        logger.debug(f"Getting async database connection for tenant: {user_tenant_info.tenant_id}")
        tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)
        logger.debug(" Async database connection established")

        # Try to find voice name in cloned voices for better metadata
        logger.debug(f" Looking up voice name for voice_id: {voice_id}")
        cloned_voice = await tenant_database.cloned_voices.find_one({"voice_id": voice_id})

        if cloned_voice:
            voice_name = cloned_voice["name"]
            logger.info(f" Found cloned voice name: {voice_name}")
            logger.debug(f" Cloned voice details: user_id={cloned_voice.get('user_id')}, created_at={cloned_voice.get('created_at')}")
        else:
            # Use voice_id as name if not found in database (could be ElevenLabs built-in voice)
            voice_name = f"Voice_{voice_id[:8]}"  # Use first 8 chars of ID as name
            logger.debug(f" Using generated voice name: {voice_name}")
            logger.info(f" Voice not found in cloned voices, likely ElevenLabs built-in voice")

        # Generate TTS audio stream
        logger.info(f" Generating TTS for text: '{text[:50]}...'")
        logger.debug(f" TTS parameters - voice_id: {voice_id}, model: eleven_multilingual_v2, format: mp3_44100_128")

        try:
            logger.debug(" Calling ElevenLabs text_to_speech.convert API")
            audio_stream = client.text_to_speech.convert(
                voice_id=voice_id,
                output_format="mp3_44100_128",
                text=text,
                model_id="eleven_multilingual_v2"
            )
            logger.debug("✅ TTS API call successful, received audio stream")
        except Exception as e:
            logger.error(f" TTS generation failed: {str(e)}")
            logger.exception("TTS generation error details:")
            raise HTTPException(status_code=500, detail=f"TTS generation failed: {str(e)}")

        # Convert generator to bytes
        logger.debug("🔄 Converting audio stream to bytes")
        try:
            audio_bytes = b"".join(audio_stream)
            logger.debug(f" Audio stream converted to bytes, size: {len(audio_bytes)} bytes")
        except Exception as e:
            logger.error(f" Failed to convert audio stream: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to process audio stream: {str(e)}")

        # Generate unique filename
        logger.debug(" Generating unique filename")
        file_id = str(uuid.uuid4())
        file_name = f"tts_{file_id}.mp3"
        logger.debug(f" Generated file_id: {file_id}, filename: {file_name}")

        # Save to MinIO
        logger.info(" Saving audio file to MinIO")
        try:
            logger.debug("Getting MinIO client and bucket info")
            minio_client = user_tenant_info.minio_client
            bucket_name = user_tenant_info.minio_bucket_name
            logger.debug(f" Using bucket: {bucket_name}")

            # Upload to MinIO
            logger.debug(f" Starting upload of {len(audio_bytes)} bytes")
            audio_buffer = BytesIO(audio_bytes)
            minio_client.put_object(
                bucket_name=bucket_name,
                object_name=f"tts/{file_name}",
                data=audio_buffer,
                length=len(audio_bytes),
                content_type="audio/mpeg"
            )

            minio_path = f"tts/{file_name}"
            logger.info(f" Audio file saved to MinIO: {minio_path}")

            # Generate presigned URL for direct access (valid for 24 hours)
            logger.debug(" Generating presigned URL")
            try:
                presigned_url = minio_client.presigned_get_object(
                    bucket_name=bucket_name,
                    object_name=minio_path,
                    expires=timedelta(hours=24)
                )
                logger.info(f"Generated presigned URL (expires in 24h)")
            except Exception as e:
                logger.error(f"Failed to generate presigned URL: {str(e)}")
                presigned_url = f"Error generating URL: {str(e)}"

        except Exception as e:
            logger.error(f" Failed to save to MinIO: {str(e)}")
            logger.exception("MinIO error details:")
            raise HTTPException(status_code=500, detail=f"Failed to save audio file: {str(e)}")

        # Save metadata to MongoDB
        logger.info(" Saving metadata to MongoDB")
        logger.debug(f" Using tenant database for tenant_id: {user_tenant_info.tenant_id}")

        try:
            logger.debug("📋 Preparing metadata document")
            tts_doc = {
                "file_id": file_id,
                "file_name": file_name,
                "minio_path": minio_path,
                "presigned_url": presigned_url,
                "text": text,
                "voice_name": voice_name,
                "voice_id": voice_id,
                "file_size": len(audio_bytes),
                "user_id": user_tenant_info.user.id,
                "username": user_tenant_info.user.username,
                "created_at": datetime.now(timezone.utc),
                "file_type": "tts_audio"
            }
            logger.debug(f"📄 Document prepared with {len(tts_doc)} fields")

            # Store in media collection
            logger.debug("💾 Inserting document into media collection")
            result = await tenant_database.media.insert_one(tts_doc)
            logger.debug(f" Document inserted with _id: {result.inserted_id}")
            logger.info(f" Metadata saved to MongoDB with file_id: {file_id}")

        except Exception as e:
            logger.error(f" Failed to save metadata: {str(e)}")
            logger.exception("MongoDB metadata save error details:")
            # Don't raise exception here as file is already saved to MinIO

        # Return response with metadata
        logger.debug("Preparing response object")
        try:
            response = TTSResponse(
                message="TTS audio generated and saved successfully",
                file_id=file_id,
                file_name=file_name,
                minio_path=minio_path,
                presigned_url=presigned_url,
                text=text,
                voice_name=voice_name,
                created_at=datetime.now(timezone.utc)
            )
            logger.debug(" Response object created successfully")
        except Exception as e:
            logger.error(f" Failed to create response object: {str(e)}")
            logger.exception("Response creation error details:")
            raise HTTPException(status_code=500, detail=f"Failed to create response: {str(e)}")

        logger.info(" TTS generation completed successfully")
        logger.debug(f"Final stats - file_size: {len(audio_bytes)} bytes, voice: {voice_name}, duration: processing complete")
        return response

    except HTTPException as http_exc:
        # Re-raise HTTP exceptions (like voice not found)
        logger.debug(f" Re-raising HTTP exception: {http_exc.status_code} - {http_exc.detail}")
        raise
    except Exception as e:
        logger.error(f" Unexpected error in TTS generation: {str(e)}")
        logger.exception("Unexpected error details:")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

