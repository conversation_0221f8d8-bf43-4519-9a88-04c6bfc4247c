from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, Depends
from fastapi.responses import JSONResponse, HTMLResponse, RedirectResponse
from fastapi.security import OAuth2PasswordBearer
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import google.genai as genai
from dotenv import load_dotenv
import os
import logging
import tempfile
from datetime import timedelta, datetime
# Import authentication modules
from core.security import create_access_token, verify_password, get_tenant_info
from core.database import get_tenant_id_and_name_from_slug, get_async_db_from_tenant_id
from models.security import OAuth2PasswordRequestFormWithClientID
from core.helper.mongo_helper import convert_objectid_to_str
from voice_cloning import router as voice_cloning_router
from app.v2.voice_cloning_v2 import router as inference_router
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# MongoDB collection names
TRANSCRIPTIONS_COLLECTION = "transcriptions"

app = FastAPI(title="Voice Processing API")
app.mount(path= "/v2",app = inference_router)
# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Routes for web interface
@app.get("/", response_class=HTMLResponse)
async def root():
    # Redirect to login page
    return RedirectResponse(url="/docs")



def transcribe_audio_gemini(file_content: bytes, filename: str, api_key: str):
    # List of word items, each with the word text and its timestamp (if available)
    response_schema = {
        "type": "OBJECT",
        "properties": {
            "transcript": {
                "type": "ARRAY",
                "items": {
                    "type": "OBJECT",
                    "properties": {
                        "word": {"type": "STRING"},
                        "start_time": {"type": "STRING"},  # Model-generated timestamps as "00:00:05.230"
                        "end_time": {"type": "STRING"}
                    },
                    "required": ["word"]
                }
            }
        },
        "required": ["transcript"]
    }

    # Initialize Gemini client
    client = genai.Client(api_key=api_key)

    # Create a temporary file for upload
    with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1]) as temp_file:
        temp_file.write(file_content)
        temp_file_path = temp_file.name

    try:
        # Upload file to Gemini
        audio_file = client.files.upload(file=temp_file_path)

        # Generate transcription
        response = client.models.generate_content(
            model="gemini-2.5-flash-preview-05-20",
            contents=[audio_file, "Transcribe this audio into a word-level JSON transcript with start_time and end_time."],
            config={"response_mime_type": "application/json", "response_schema": response_schema}
        )
        return response.parsed
    finally:
        # Clean up temporary file
        os.unlink(temp_file_path)

@app.post("/login")
async def login(form_data: OAuth2PasswordRequestFormWithClientID = Depends()):
    # Find the database name of that tenant
    try:
        result = get_tenant_id_and_name_from_slug(form_data.client_id)

        tenant_id = str(result["_id"])
        tenant_database = get_async_db_from_tenant_id(tenant_id)

        # Connect to the user_collection of that database
        user = await tenant_database.users.find_one({"username": form_data.username})

        if not user:
            logger.error(f"User not found: {form_data.username}")
            raise HTTPException(status_code=401, detail="User not found")

        if not verify_password(form_data.password, user["hashed_password"]):
            logger.error(f"Incorrect credentials: {form_data.username}")
            raise HTTPException(status_code=401, detail="Incorrect credentials")

        # Create access token with user information
        access_token = create_access_token(
            data={"sub": user["username"], "role": user["role"], "tenant_id": tenant_id},
            expires_delta=timedelta(days=1)
        )

        # Convert ObjectId to string for JSON response
        user = convert_objectid_to_str(user)

        logger.info(f"User logged in: {user['username']} for tenant: {result['name']}")

        return {
            "id": user["_id"],
            "access_token": access_token,
            "token_type": "bearer",
            "username": user['username'],
            "role": user['role'],
            "tenant_id": tenant_id,
            "tenant_label": result["name"],
            "tenant_slug": form_data.client_id,
        }
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Login failed: {str(e)}")

@app.get("/verify-token")
async def verify_token(user_tenant_info = Depends(get_tenant_info)):
    """If the token is valid, return the user's details
       If the token is invalid, get_tenant_info will raise an exception
    """
    return {"valid": True, "user": user_tenant_info.user.model_dump()}

@app.post("/transcribe")
async def transcribe(file: UploadFile = File(...), user_tenant_info = Depends(get_tenant_info)):
    """
    Endpoint that forwards audio transcription requests to the GPU server.
    Requires authentication via Bearer token.
    """
    logger.info("Received transcription request")
    logger.info(f"File: {file.filename}")

    try:
        # Read the file content
        file_content = await file.read()

        # Create multipart form data for the GPU server
        import httpx
        files = {
            "audio_file": (file.filename, file_content, "audio/wav")
        }

        # Call the GPU server's transcription endpoint
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{os.getenv('TRANSCRIPTION_API_URL', 'http://*************:8002')}/transcribe",
                files=files,
                timeout=240  # Long timeout for large audio files
            )
            response.raise_for_status()

            transcription_result = response.json()
            return JSONResponse(content=transcription_result)

    except httpx.HTTPStatusError as e:
        logger.error(f"GPU server error: {e.response.text}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Transcription API error: {e.response.text}"
        )
    except httpx.RequestError as e:
        logger.error(f"Could not connect to GPU server: {str(e)}")
        raise HTTPException(
            status_code=503,
            detail=f"Service unavailable: Unable to connect to transcription API - {str(e)}"
        )
    except Exception as e:
        logger.error(f"Transcription error: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Transcription failed: {str(e)}"
        )
    
# Include the voice cloning router
app.include_router(voice_cloning_router)

# Include the TTS router
from tts import router as tts_router
app.include_router(tts_router)
