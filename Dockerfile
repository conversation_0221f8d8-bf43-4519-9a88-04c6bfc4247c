FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Install uv (dependency manager)
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Copy only dependency files first to cache layers
COPY pyproject.toml uv.lock ./

# Install dependencies using uv
RUN uv sync --frozen --no-cache

# Copy the rest of your application code
COPY . .

# Expose your app port
EXPOSE 8400

# Run the application with uvicorn (via uv)
CMD ["uv", "run", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8400"]
