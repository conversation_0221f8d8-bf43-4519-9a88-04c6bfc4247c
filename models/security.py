from fastapi import Form
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel
from typing import Optional

class OAuth2PasswordRequestFormWithClientID:
    def __init__(
        self,
        grant_type: str = Form("password"),  # No pattern here
        username: str = Form(...),
        password: str = Form(...),
        scope: str = Form(""),
        client_id: str = Form(...),  # Required field
    ):
        if grant_type and grant_type != "password":
            raise ValueError("grant_type must be 'password'")
        self.grant_type = grant_type
        self.username = username
        self.password = password
        self.scope = scope
        self.client_id = client_id

class ChangePasswordRequest(BaseModel):
    old_password: str
    new_password: str

class ResetPasswordRequest(BaseModel):
    subordinate_id: Optional[str] = None
