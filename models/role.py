from pydantic import BaseModel, Field, field_validator, model_validator
from typing import Any, Dict, Literal, List, Optional
from pymongo.database import Database
from datetime import datetime
from bson import ObjectId

class Role(BaseModel):
    """
    User document from the tenant database -> users collection
    """
    id: Any = Field(alias="_id")
    name: str = Field(..., description="Name of the role")


    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)
    

from pydantic import BaseModel, field_validator
from typing import Literal

class RoleUpdateRequest(BaseModel):
    user_id: str
    role: Literal["admin", "supervisor", "agent"]

    @field_validator("user_id")
    @classmethod
    def validate_object_id(cls, value):
        if not ObjectId.is_valid(value):
            raise ValueError("Invalid user ID format")
        return value
