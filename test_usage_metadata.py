#!/usr/bin/env python3
"""
Test script to verify the usage metadata implementation in TTS responses
and cost tracking API functionality.
"""

import asyncio
import httpx
import json
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000/v2"
TEST_TOKEN = "your_test_token_here"  # Replace with actual token

async def test_gemini_tts_with_usage_metadata():
    """Test the Gemini TTS endpoint to verify usage metadata is included in response."""
    print("Testing Gemini TTS with usage metadata...")
    
    async with httpx.AsyncClient() as client:
        # Test data
        test_data = {
            "text": "Hello, this is a test of the Gemini TTS service with usage metadata tracking.",
            "voice_name": "Aoede",
            "style_instructions": "Speak in a calm and professional tone."
        }
        
        headers = {
            "Authorization": f"Bearer {TEST_TOKEN}",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        try:
            response = await client.post(
                f"{BASE_URL}/gemini-tts/generate",
                data=test_data,
                headers=headers,
                timeout=60.0
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ TTS Response received successfully!")
                print(f"File ID: {result.get('file_id')}")
                print(f"Message: {result.get('message')}")
                
                # Check for usage metadata
                usage_metadata = result.get('usage_metadata')
                if usage_metadata:
                    print("✅ Usage metadata found in response:")
                    print(f"  - Prompt tokens: {usage_metadata.get('prompt_token_count')}")
                    print(f"  - Candidates tokens: {usage_metadata.get('candidates_token_count')}")
                    print(f"  - Total tokens: {usage_metadata.get('total_token_count')}")
                    print(f"  - Estimated cost: ${usage_metadata.get('estimated_cost_usd'):.6f}")
                else:
                    print("❌ Usage metadata not found in response")
                
                return result
            else:
                print(f"❌ TTS request failed: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error testing TTS: {str(e)}")
            return None

async def test_cost_tracking_summary():
    """Test the cost tracking summary endpoint."""
    print("\nTesting cost tracking summary...")
    
    async with httpx.AsyncClient() as client:
        headers = {
            "Authorization": f"Bearer {TEST_TOKEN}"
        }
        
        try:
            response = await client.get(
                f"{BASE_URL}/cost-tracking/summary",
                headers=headers,
                timeout=30.0
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Cost tracking summary received successfully!")
                
                summary = result.get('summary', {})
                print(f"Total cost: ${summary.get('total_cost_usd', 0):.6f}")
                print(f"Total requests: {summary.get('total_requests', 0)}")
                print(f"Total tokens: {summary.get('total_tokens', 0)}")
                print(f"Average cost per request: ${summary.get('average_cost_per_request', 0):.6f}")
                
                # Service breakdown
                service_breakdown = result.get('service_breakdown', [])
                if service_breakdown:
                    print("\nService breakdown:")
                    for service in service_breakdown:
                        print(f"  - {service.get('service_name')}: ${service.get('total_cost_usd', 0):.6f} ({service.get('total_requests', 0)} requests)")
                
                # User breakdown
                user_breakdown = result.get('user_breakdown', [])
                if user_breakdown:
                    print(f"\nUser breakdown ({len(user_breakdown)} users):")
                    for user in user_breakdown[:3]:  # Show first 3 users
                        print(f"  - {user.get('username')}: ${user.get('total_cost_usd', 0):.6f} ({user.get('total_requests', 0)} requests)")
                
                return result
            else:
                print(f"❌ Cost tracking request failed: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error testing cost tracking: {str(e)}")
            return None

async def test_detailed_usage():
    """Test the detailed usage endpoint."""
    print("\nTesting detailed usage records...")
    
    async with httpx.AsyncClient() as client:
        headers = {
            "Authorization": f"Bearer {TEST_TOKEN}"
        }
        
        params = {
            "limit": 10,
            "page": 1
        }
        
        try:
            response = await client.get(
                f"{BASE_URL}/cost-tracking/detailed-usage",
                headers=headers,
                params=params,
                timeout=30.0
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Detailed usage records received successfully!")
                
                print(f"Total records: {result.get('total_records', 0)}")
                
                records = result.get('records', [])
                if records:
                    print(f"\nShowing {len(records)} recent records:")
                    for i, record in enumerate(records[:3], 1):
                        print(f"  {i}. Service: {record.get('service')}")
                        print(f"     User: {record.get('username')}")
                        print(f"     Cost: ${record.get('cost_usd', 0):.6f}")
                        print(f"     Text length: {record.get('text_length', 0)} chars")
                        print(f"     Created: {record.get('created_at')}")
                        print()
                
                pagination = result.get('pagination', {})
                print(f"Pagination: Page {pagination.get('current_page', 1)} of {pagination.get('total_pages', 1)}")
                
                return result
            else:
                print(f"❌ Detailed usage request failed: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error testing detailed usage: {str(e)}")
            return None

async def main():
    """Run all tests."""
    print("🚀 Starting usage metadata and cost tracking tests...\n")
    
    # Test 1: TTS with usage metadata
    tts_result = await test_gemini_tts_with_usage_metadata()
    
    # Test 2: Cost tracking summary
    summary_result = await test_cost_tracking_summary()
    
    # Test 3: Detailed usage records
    detailed_result = await test_detailed_usage()
    
    print("\n" + "="*50)
    print("📊 Test Summary:")
    print(f"✅ TTS with usage metadata: {'PASS' if tts_result else 'FAIL'}")
    print(f"✅ Cost tracking summary: {'PASS' if summary_result else 'FAIL'}")
    print(f"✅ Detailed usage records: {'PASS' if detailed_result else 'FAIL'}")
    print("="*50)

if __name__ == "__main__":
    print("Usage Metadata and Cost Tracking Test Script")
    print("=" * 50)
    print("Before running this test:")
    print("1. Make sure the API server is running on localhost:8000")
    print("2. Replace TEST_TOKEN with a valid authentication token")
    print("3. Ensure you have the required dependencies installed")
    print("=" * 50)
    
    # Uncomment the line below to run the tests
    # asyncio.run(main())
    
    print("To run the tests, uncomment the last line and execute this script.")
