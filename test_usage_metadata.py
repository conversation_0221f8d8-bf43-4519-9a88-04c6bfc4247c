#!/usr/bin/env python3
"""
Test script to verify the usage metadata implementation in TTS responses
and cost tracking API functionality.
"""

import asyncio
import httpx
import json
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000/v2"
TEST_TOKEN = "your_test_token_here"  # Replace with actual token

async def test_gemini_tts_with_usage_metadata():
    """Test the Gemini TTS endpoint to verify usage metadata is included in response."""
    print("Testing Gemini TTS with usage metadata...")
    
    async with httpx.AsyncClient() as client:
        # Test data
        test_data = {
            "text": "Hello, this is a test of the Gemini TTS service with usage metadata tracking.",
            "voice_name": "Aoede"
        }
        
        headers = {
            "Authorization": f"Bearer {TEST_TOKEN}",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        try:
            response = await client.post(
                f"{BASE_URL}/gemini-tts/generate",
                data=test_data,
                headers=headers,
                timeout=60.0
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ TTS Response received successfully!")
                print(f"File ID: {result.get('file_id')}")
                print(f"Message: {result.get('message')}")
                
                # Check for usage metadata
                usage_metadata = result.get('usage_metadata')
                if usage_metadata:
                    print("✅ Usage metadata found in response:")
                    print(f"  - Prompt tokens: {usage_metadata.get('prompt_token_count')}")
                    print(f"  - Candidates tokens: {usage_metadata.get('candidates_token_count')}")
                    print(f"  - Total tokens: {usage_metadata.get('total_token_count')}")
                    print(f"  - Estimated cost: ${usage_metadata.get('estimated_cost_usd'):.6f} USD")
                    print(f"  - Estimated cost: Rs.{usage_metadata.get('estimated_cost_npr'):.4f} NPR")
                else:
                    print("❌ Usage metadata not found in response")
                
                return result
            else:
                print(f"❌ TTS request failed: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error testing TTS: {str(e)}")
            return None

async def test_cost_tracking_summary():
    """Test the cost tracking summary endpoint."""
    print("\nTesting cost tracking summary...")
    
    async with httpx.AsyncClient() as client:
        headers = {
            "Authorization": f"Bearer {TEST_TOKEN}"
        }
        
        try:
            response = await client.get(
                f"{BASE_URL}/cost-tracking/summary",
                headers=headers,
                timeout=30.0
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Cost tracking summary received successfully!")
                
                summary = result.get('summary', {})
                print(f"Total cost: ${summary.get('total_cost_usd', 0):.6f} USD")
                print(f"Total cost: Rs.{summary.get('total_cost_npr', 0):.4f} NPR")
                print(f"Total requests: {summary.get('total_requests', 0)}")
                print(f"Total tokens: {summary.get('total_tokens', 0)}")
                print(f"Average cost per request: ${summary.get('average_cost_per_request_usd', 0):.6f} USD")
                print(f"Average cost per request: Rs.{summary.get('average_cost_per_request_npr', 0):.4f} NPR")
                print(f"Date range: {summary.get('date_range', {}).get('start')} to {summary.get('date_range', {}).get('end')}")
                
                return result
            else:
                print(f"❌ Cost tracking request failed: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error testing cost tracking: {str(e)}")
            return None



async def main():
    """Run all tests."""
    print("🚀 Starting usage metadata and cost tracking tests...\n")

    # Test 1: TTS with usage metadata
    tts_result = await test_gemini_tts_with_usage_metadata()

    # Test 2: Cost tracking summary
    summary_result = await test_cost_tracking_summary()

    print("\n" + "="*50)
    print("📊 Test Summary:")
    print(f"✅ TTS with usage metadata: {'PASS' if tts_result else 'FAIL'}")
    print(f"✅ Cost tracking summary: {'PASS' if summary_result else 'FAIL'}")
    print("="*50)

if __name__ == "__main__":
    print("Usage Metadata and Cost Tracking Test Script")
    print("=" * 50)
    print("Before running this test:")
    print("1. Make sure the API server is running on localhost:8000")
    print("2. Replace TEST_TOKEN with a valid authentication token")
    print("3. Ensure you have the required dependencies installed")
    print("=" * 50)
    
    # Uncomment the line below to run the tests
    # asyncio.run(main())
    
    print("To run the tests, uncomment the last line and execute this script.")
