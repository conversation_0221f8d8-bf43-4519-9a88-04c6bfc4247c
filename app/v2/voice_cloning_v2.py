from fastapi import <PERSON><PERSON><PERSON>, Depends, Query
from fastapi import Form, HTTPException
from fastapi.responses import JSONResponse
from fastapi.openapi.utils import get_openapi
import httpx
import os
from typing import Optional, Dict, Any, Annotated
import logging
import uuid
from datetime import datetime, timezone, timedelta
from io import BytesIO
from bson import ObjectId
from app.v2.user_management import router as user_management_router
from app.v2.voice_cloning import router as voice_cloning_router
from app.v2.gemini_tts import router as gemini_tts_router
from app.v2.cost_tracking import router as cost_tracking_router
from core.security import get_tenant_info
from core.database import get_async_db_from_tenant_id
from models.user import UserTenantDB

logger = logging.getLogger(__name__)

# Configuration
EXISTING_API_URL_CH = os.getenv("INFERENCE_API_URL", "http://172.16.16.155:8002")
TIMEOUT = 240  # Increased timeout for audio processing

# Helper function to fetch style presets from MongoDB
async def get_style_presets(user_tenant_info: UserTenantDB) -> Dict[str, Dict[str, Any]]:
    """
    Fetch style presets from MongoDB config collection.

    Returns:
        Dictionary of style presets with their parameters
    """
    try:
        # Get database connection
        tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)

        # Fetch style presets from config collection
        config_doc = await tenant_database.config.find_one({"style_presets": {"$exists": True}})

        if not config_doc or "style_presets" not in config_doc:
            logger.warning("Style presets not found in config collection, using default presets")
            # Return default presets as fallback
            return {
                "default": {
                    "exaggeration": 0.5,
                    "cfg_weight": 0.5,
                    "temperature": 0.5,
                    "seed": 0
                }
            }

        style_presets = config_doc["style_presets"]
        logger.info(f"Loaded {len(style_presets)} style presets from database: {list(style_presets.keys())}")
        return style_presets

    except Exception as e:
        logger.error(f"Error fetching style presets from database: {str(e)}")
        # Return default preset as fallback
        return {
            "default": {
                "exaggeration": 0.5,
                "cfg_weight": 0.5,
                "temperature": 0.5,
                "seed": 0
            }
        }


# Create router
router = FastAPI(title="Voice Cloning_v2")
router.include_router(user_management_router)
router.include_router(voice_cloning_router)
router.include_router(gemini_tts_router)
router.include_router(cost_tracking_router)

# Custom OpenAPI schema modification 

# @router.post("/speak")
# async def inference_endpoint(
#     voice_id: str = Form(...),
#     reference_text: Optional[str] = Form(None),
#     generation_text: str = Form(...),
#     user_tenant_info: UserTenantDB = Depends(get_tenant_info)
# ):
#     """
#     Endpoint that takes a voice_id and text, retrieves the voice file from database,
#     then calls inference API. Uploads both input and output audio files to MinIO
#     and returns JSON with presigned URLs.
#     Requires authentication via Bearer token.

#     Args:
#         voice_id: Voice clone ID to use for inference
#         reference_text: Optional reference text (can be None/empty, will be passed as-is)
#         generation_text: Required generation text
#         user_tenant_info: Authenticated user information (injected by dependency)

#     Returns:
#         JSON response with input_media and output_media presigned URLs
#     """
#     try:
#         # Log authenticated API usage
#         logger.info(f"Inference API called by user: {user_tenant_info.user.username} from tenant: {user_tenant_info.tenant_id}")

#         # Validate voice_id format
#         try:
#             ObjectId(voice_id)
#         except Exception:
#             raise HTTPException(status_code=400, detail="Invalid voice_id format")

#         # Validate generation_text is not empty
#         if not generation_text or len(generation_text.strip()) == 0:
#             raise HTTPException(
#                 status_code=400,
#                 detail="generation_text cannot be empty"
#             )

#         # Get database connection
#         tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)

#         # Retrieve voice from database
#         logger.info(f"Retrieving voice with voice_id: {voice_id}")
#         voice = await tenant_database.cloned_voices.find_one({"_id": ObjectId(voice_id)})

#         if not voice:
#             raise HTTPException(status_code=404, detail="Voice not found")

#         # Get voice metadata
#         user_id = voice.get("user_id")
#         user_name = voice.get("voice_name")
#         audio_files = voice.get("audio_files", [])

#         if not audio_files:
#             raise HTTPException(status_code=400, detail="No audio files found for this voice")

#         # Use the first audio file (since we only store one file per voice now)
#         audio_file_info = audio_files[0]
#         voice_minio_path = audio_file_info["minio_path"]
#         original_filename = audio_file_info["file_name"]

#         logger.info(f"Using audio file: {voice_minio_path}")

#         # Get MinIO client and bucket info
#         logger.info("Getting MinIO client for file retrieval")
#         minio_client = user_tenant_info.minio_client
#         bucket_name = user_tenant_info.minio_bucket_name

#         # Download the audio file from MinIO
#         try:
#             response = minio_client.get_object(bucket_name, voice_minio_path)
#             file_content = response.read()
#             response.close()
#             logger.info(f"Retrieved audio file size: {len(file_content)} bytes")
#         except Exception as e:
#             logger.error(f"Failed to retrieve audio file from MinIO: {str(e)}")
#             raise HTTPException(status_code=500, detail=f"Failed to retrieve voice audio file: {str(e)}")

#         # Step 1: Upload input file to MinIO for inference tracking
#         logger.info("Uploading input audio file to MinIO")
#         input_file_extension = os.path.splitext(original_filename)[1] if original_filename else '.wav'
#         input_minio_path = f"inference/{user_id}/{user_name}/input_{uuid.uuid4()}{input_file_extension}"

#         try:
#             input_buffer = BytesIO(file_content)
#             minio_client.put_object(
#                 bucket_name=bucket_name,
#                 object_name=input_minio_path,
#                 data=input_buffer,
#                 length=len(file_content),
#                 content_type="audio/wav"  # Default content type
#             )
#             logger.info(f"Input file uploaded to MinIO: {input_minio_path}")
#         except Exception as e:
#             logger.error(f"Failed to upload input file to MinIO: {str(e)}")
#             raise HTTPException(status_code=500, detail=f"Failed to save input file: {str(e)}")

#         # Prepare multipart form data for the existing API
#         files = {
#             "ref_audio": (original_filename, file_content, "audio/wav")
#         }

#         data = {
#             "ref_text": reference_text,  # Always include, even if None/empty
#             "gen_text": generation_text
#         }

#         # Step 2: Call existing inference API with multipart form data
#         logger.info("Calling external inference API")
#         async with httpx.AsyncClient() as client:
#             response = await client.post(
#                 f"{EXISTING_API_URL_F5}/synthesize/",
#                 files=files,
#                 data=data,
#                 timeout=TIMEOUT
#             )
#             response.raise_for_status()

#         # Get the generated audio content
#         output_audio_content = response.content
#         logger.info(f"Generated audio size: {len(output_audio_content)} bytes")

#         # Step 3: Upload output file to MinIO
#         logger.info("Uploading generated audio file to MinIO")
#         output_filename = f"output_{uuid.uuid4()}.wav"
#         output_minio_path = f"inference/{user_id}/{user_name}/{output_filename}"

#         try:
#             output_buffer = BytesIO(output_audio_content)
#             minio_client.put_object(
#                 bucket_name=bucket_name,
#                 object_name=output_minio_path,
#                 data=output_buffer,
#                 length=len(output_audio_content),
#                 content_type=response.headers.get('content-type', 'audio/wav')
#             )
#             logger.info(f"Output file uploaded to MinIO: {output_minio_path}")
#         except Exception as e:
#             logger.error(f"Failed to upload output file to MinIO: {str(e)}")
#             raise HTTPException(status_code=500, detail=f"Failed to save output file: {str(e)}")

#         # Step 4: Generate presigned URLs for both files
#         logger.info("Generating presigned URLs")
#         try:
#             input_presigned_url = minio_client.presigned_get_object(
#                 bucket_name=bucket_name,
#                 object_name=input_minio_path,
#                 expires=timedelta(hours=24)
#             )

#             output_presigned_url = minio_client.presigned_get_object(
#                 bucket_name=bucket_name,
#                 object_name=output_minio_path,
#                 expires=timedelta(hours=24)
#             )
#             logger.info("Presigned URLs generated successfully")
#         except Exception as e:
#             logger.error(f"Failed to generate presigned URLs: {str(e)}")
#             input_presigned_url = f"Error generating URL: {str(e)}"
#             output_presigned_url = f"Error generating URL: {str(e)}"

#         # Step 5: Save metadata to database
#         logger.info("Saving inference metadata to inference_results collection")
#         try:
#             tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)

#             inference_doc = {
#                 "user_id": user_id,
#                 "user_name": user_name,
#                 "username": user_tenant_info.user.username,
#                 "input_minio_path": input_minio_path,
#                 "output_minio_path": output_minio_path,
#                 "input_presigned_url": input_presigned_url,
#                 "output_presigned_url": output_presigned_url,
#                 "reference_text": reference_text,
#                 "generation_text": generation_text,
#                 "input_file_size": len(file_content),
#                 "output_file_size": len(output_audio_content),
#                 "created_at": datetime.now(timezone.utc),
#                 "file_type": "inference_audio",
#                 "model_type": "f5_tts"  # Identify which model was used
#             }

#             result = await tenant_database.inference_results.insert_one(inference_doc)
#             logger.info(f"Metadata saved to inference_results collection with _id: {result.inserted_id}")
#         except Exception as e:
#             logger.error(f"Failed to save metadata to inference_results collection: {str(e)}")
#             # Don't fail the request if metadata save fails

#         # Step 6: Return JSON response with presigned URLs
#         logger.info("Inference completed successfully")
#         return JSONResponse(
#             content={
#                 "message": "Voice inference completed successfully",
#                 "input_media": input_presigned_url,
#                 "output_media": output_presigned_url,
#                 "generation_text": generation_text,
#                 "reference_text": reference_text,
#                 "user_id": user_id,
#                 "user_name": user_name,
#                 "input_file_size": len(file_content),
#                 "output_file_size": len(output_audio_content),
#                 "created_at": datetime.now().isoformat(),
#                 "minio_paths": {
#                     "input": input_minio_path,
#                     "output": output_minio_path
#                 }
#             },
#             status_code=200
#         )
        
#     except httpx.HTTPStatusError as e:
#         raise HTTPException(
#             status_code=e.response.status_code,
#             detail=f"Inference API error: {e.response.text}"
#         )
#     except httpx.RequestError as e:
#         raise HTTPException(
#             status_code=503,
#             detail=f"Service unavailable: Unable to connect to inference API - {str(e)}"
#         )
#     except Exception as e:
#         raise HTTPException(
#             status_code=500,
#             detail=f"Internal server error: {str(e)}"
#         )
    

@router.post("/speak")
async def inference(
    voice_id: str = Form(...),
    generation_text: str = Form(...),
    user_tenant_info: UserTenantDB = Depends(get_tenant_info),
    style_preset: str = Form(default="default")
):
    """
    Endpoint that takes a voice_id and text, retrieves the voice file from database,
    then calls  inference API. Uploads both input and output audio files to MinIO
    then calls  inference API. Uploads both input and output audio files to MinIO
    and returns JSON with presigned URLs.
    Requires authentication via Bearer token.

    Args:
        voice_id: Voice clone ID to use for inference
        generation_text: Required generation text
        style_preset: Style preset (Literal: 'default', 'natural', 'dramatic', 'presentation')
        user_tenant_info: Authenticated user information (injected by dependency)
        style_preset: Style preset name (dynamically loaded from MongoDB config collection)

    Style Presets:
        Style presets are dynamically loaded from the MongoDB config collection.
        Available presets and their parameters depend on the configuration stored in the database.
        Use the /style-presets endpoint to see all available options.
        If no presets are found, a default preset will be used.

    Returns:
        JSON response with input_media and output_media presigned URLs
    """
    try:
        # Log authenticated API usage
        logger.info(f" Inference API called by user: {user_tenant_info.user.username} from tenant: {user_tenant_info.tenant_id}")

        # Validate voice_id format
        try:
            ObjectId(voice_id)
        except Exception:
            raise HTTPException(status_code=400, detail="Invalid voice_id format")

        # Validate generation_text is not empty
        if not generation_text or len(generation_text.strip()) == 0:
            raise HTTPException(
                status_code=400,
                detail="generation_text cannot be empty"
            )

        # Fetch style presets from database
        style_presets = await get_style_presets(user_tenant_info)

        # Validate style_preset exists
        if style_preset.lower() not in style_presets:
            available_presets = list(style_presets.keys())
            raise HTTPException(
                status_code=400,
                detail=f"Invalid style_preset '{style_preset}'. Available presets: {available_presets}"
            )

        # Apply style preset
        preset = style_presets[style_preset.lower()]
        final_exaggeration = preset["exaggeration"]
        final_cfg_weight = preset["cfg_weight"]
        final_temperature = preset["temperature"]
        final_seed = preset["seed"]

        logger.info(f"Using style preset '{style_preset}': exaggeration={final_exaggeration}, cfg_weight={final_cfg_weight}, temperature={final_temperature}, seed={final_seed}")

        # Get database connection
        tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)

        # Retrieve voice from database
        logger.info(f"Retrieving voice with voice_id: {voice_id}") 
        logger.info(f"Retrieving voice with voice_id: {voice_id}") 
        voice = await tenant_database.cloned_voices.find_one({"_id": ObjectId(voice_id)})

        if not voice:
            raise HTTPException(status_code=404, detail="Voice not found")

        # Get voice metadata
        user_id = voice.get("user_id")
        user_name = voice.get("voice_name")
        audio_files = voice.get("audio_files", [])

        if not audio_files:
            raise HTTPException(status_code=400, detail="No audio files found for this voice")

        # Use the first audio file (since we only store one file per voice now)
        audio_file_info = audio_files[0]
        voice_minio_path = audio_file_info["minio_path"]
        original_filename = audio_file_info["file_name"]

        logger.info(f"Using audio file: {voice_minio_path}")

        # Get MinIO client and bucket info
        logger.info("Getting MinIO client for file retrieval")
        minio_client = user_tenant_info.minio_client
        bucket_name = user_tenant_info.minio_bucket_name

        # Download the audio file from MinIO
        try:
            response = minio_client.get_object(bucket_name, voice_minio_path)
            file_content = response.read()
            response.close()
            logger.info(f"Retrieved audio file size: {len(file_content)} bytes")
        except Exception as e:
            logger.error(f"Failed to retrieve audio file from MinIO: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to retrieve voice audio file: {str(e)}")

        # Step 1: Upload input file to MinIO for inference tracking
        logger.info("Uploading input audio file to MinIO")
        input_file_extension = os.path.splitext(original_filename)[1] if original_filename else '.wav'
        input_minio_path = f"inference_chatterbox/{user_id}/{user_name}/input_{uuid.uuid4()}{input_file_extension}"
        input_minio_path = f"inference_chatterbox/{user_id}/{user_name}/input_{uuid.uuid4()}{input_file_extension}"

        try:
            input_buffer = BytesIO(file_content)
            minio_client.put_object(
                bucket_name=bucket_name,
                object_name=input_minio_path,
                data=input_buffer,
                length=len(file_content),
                content_type="audio/wav"  # Default content type
            )
            logger.info(f"Input file uploaded to MinIO: {input_minio_path}")
        except Exception as e:
            logger.error(f"Failed to upload input file to MinIO: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to save input file: {str(e)}")

        # Prepare multipart form data for the Chatterbox API
        # Prepare multipart form data for the Chatterbox API
        files = {
            "ref_audio": (original_filename, file_content, "audio/wav")
        }

        data = {
            "text": generation_text,  # Chatterbox uses 'text' instead of separate ref_text/gen_text
            "exaggeration": final_exaggeration,
            "temperature": final_temperature,
            "seed": final_seed,
            "cfg_weight": final_cfg_weight,
            "text": generation_text,  # Chatterbox uses 'text' instead of separate ref_text/gen_text
            "exaggeration": final_exaggeration,
            "temperature": final_temperature,
            "seed": final_seed,
            "cfg_weight": final_cfg_weight
        }

        # Step 2: Call Chatterbox inference API with multipart form data
        logger.info("Calling  inference API")
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{EXISTING_API_URL_CH}/synthesize/",
                files=files,
                data=data,
                timeout=TIMEOUT
            )
            response.raise_for_status()

        # Get the generated audio content
        output_audio_content = response.content
        logger.info(f"Generated audio size: {len(output_audio_content)} bytes")

        # Step 3: Upload output file to MinIO
        logger.info("Uploading generated audio file to MinIO")
        output_filename = f"output_{uuid.uuid4()}.wav"
        output_minio_path = f"inference_chatterbox/{user_id}/{user_name}/{output_filename}"
        output_minio_path = f"inference_chatterbox/{user_id}/{user_name}/{output_filename}"

        try:
            output_buffer = BytesIO(output_audio_content)
            minio_client.put_object(
                bucket_name=bucket_name,
                object_name=output_minio_path,
                data=output_buffer,
                length=len(output_audio_content),
                content_type=response.headers.get('content-type', 'audio/wav')
            )
            logger.info(f"Output file uploaded to MinIO: {output_minio_path}")
        except Exception as e:
            logger.error(f"Failed to upload output file to MinIO: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to save output file: {str(e)}")

        # Step 4: Generate presigned URLs for both files
        logger.info("Generating presigned URLs")
        try:
            input_presigned_url = minio_client.presigned_get_object(
                bucket_name=bucket_name,
                object_name=input_minio_path,
                expires=timedelta(hours=24)
            )

            output_presigned_url = minio_client.presigned_get_object(
                bucket_name=bucket_name,
                object_name=output_minio_path,
                expires=timedelta(hours=24)
            )
            logger.info("Presigned URLs generated successfully")
        except Exception as e:
            logger.error(f"Failed to generate presigned URLs: {str(e)}")
            input_presigned_url = f"Error generating URL: {str(e)}"
            output_presigned_url = f"Error generating URL: {str(e)}"

        # Step 5: Save metadata to database
        logger.info("Saving Chatterbox inference metadata to inference_results collection")
        logger.info("Saving Chatterbox inference metadata to inference_results collection")
        try:
            inference_doc = {
                "user_id": user_id,
                "user_name": user_name,
                "input_minio_path": input_minio_path,
                "output_minio_path": output_minio_path,
                "input_presigned_url": input_presigned_url,
                "output_presigned_url": output_presigned_url,
                "generation_text": generation_text,
                "style_preset": style_preset,
                "exaggeration": final_exaggeration,
                "temperature": final_temperature,
                "seed": final_seed,
                "cfg_weight": final_cfg_weight,
                "style_preset": style_preset,
                "exaggeration": final_exaggeration,
                "temperature": final_temperature,
                "seed": final_seed,
                "cfg_weight": final_cfg_weight,
                "input_file_size": len(file_content),
                "output_file_size": len(output_audio_content),
                "created_at": datetime.now(timezone.utc),
                "file_type": "inference_audio",
            }

            result = await tenant_database.inference_results.insert_one(inference_doc)
            logger.info(f"Metadata saved to inference_results collection with _id: {result.inserted_id}")
            result = await tenant_database.inference_results.insert_one(inference_doc)
            logger.info(f"Metadata saved to inference_results collection with _id: {result.inserted_id}")
        except Exception as e:
            logger.error(f"Failed to save metadata to inference_results collection: {str(e)}")
            logger.error(f"Failed to save metadata to inference_results collection: {str(e)}")
            # Don't fail the request if metadata save fails

        # Step 6: Return JSON response with presigned URLs
        logger.info(" inference completed successfully")
        return JSONResponse(
            content={
                "message": " voice inference completed successfully",
                "input_media": input_presigned_url,
                "output_media": output_presigned_url,
                "generation_text": generation_text,
                "style_preset": style_preset,
                "parameters": {
                    "exaggeration": final_exaggeration,
                    "temperature": final_temperature,
                    "seed": final_seed,
                    "cfg_weight": final_cfg_weight
                },
                "user_id": user_id,
                "user_name": user_name,
                "input_file_size": len(file_content),
                "output_file_size": len(output_audio_content),
                "created_at": datetime.now().isoformat(),
                "minio_paths": {
                    "input": input_minio_path,
                    "output": output_minio_path
                }
            },
            status_code=200
        )


    except HTTPException:
        # Re-raise HTTPExceptions (like 400 validation errors) without modification
        raise
    except httpx.HTTPStatusError as e:
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f" Inferencing API error: {e.response.text}"
        )
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=503,
            detail=f"Service unavailable: Unable to connect to Inferencing API - {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/get_presets")
async def get_presets(
    user_tenant_info: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get all available style presets from MongoDB config collection.
    Requires authentication via Bearer token.

    Returns:
        JSON response with all available style presets and their parameters
    """
    try:
        # Log authenticated API usage
        logger.info(f"Get presets API called by user: {user_tenant_info.user.username} from tenant: {user_tenant_info.tenant_id}")

        # Fetch style presets from database using existing function
        style_presets = await get_style_presets(user_tenant_info)

        # Count total presets
        total_presets = len(style_presets)

        # Log successful retrieval
        logger.info(f"Successfully retrieved {total_presets} style presets for user: {user_tenant_info.user.username}")

        # Return JSON response with all presets
        return JSONResponse(
            content={
                "message": "Style presets retrieved successfully",
                "total_presets": total_presets,
                "presets": style_presets,
                "available_preset_names": list(style_presets.keys()),
                "retrieved_at": datetime.now().isoformat(),
                "user": user_tenant_info.user.username,
                "tenant_id": user_tenant_info.tenant_id
            },
            status_code=200
        )

    except HTTPException:
        # Re-raise HTTPExceptions without modification
        raise
    except Exception as e:
        logger.error(f"Error retrieving style presets for user {user_tenant_info.user.username}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve style presets: {str(e)}"
        )

