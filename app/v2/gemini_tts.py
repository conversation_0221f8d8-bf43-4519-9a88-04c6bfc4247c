from fastapi import APIRouter, Depends, HTTPException, Form
from pydantic import BaseModel
from google import genai
from google.genai import types
import struct
import logging
import uuid
from datetime import datetime, timezone, timedelta
from io import BytesIO
from typing import Optional, Literal

# Custom imports
from core.security import get_tenant_info
from core.database import get_async_db_from_tenant_id
from models.user import UserTenantDB

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="", tags=["Gemini TTS"])

class UsageMetadata(BaseModel):
    prompt_token_count: int
    candidates_token_count: int
    total_token_count: int
    estimated_cost_usd: float

class GeminiTTSResponse(BaseModel):
    message: str
    file_id: str
    file_name: str
    minio_path: str
    presigned_url: str
    text: str
    voice_name: str
    created_at: datetime
    usage_metadata: UsageMetadata

# Define available voices (2 male, 2 female)
VoiceName = Literal["<PERSON><PERSON>", "Puck", "<PERSON><PERSON>", "Aoede"]

# Gemini TTS pricing (as of 2025) - per 1000 characters
GEMINI_TTS_PRICING = {
    "input_cost_per_1k_chars": 0.000125,  # $0.000125 per 1000 input characters
}

def calculate_tts_cost(text: str, usage_metadata=None) -> float:
    """
    Calculate the estimated cost for Gemini TTS based on input text length.

    Args:
        text: The input text for TTS
        usage_metadata: Optional usage metadata from the API response

    Returns:
        Estimated cost in USD
    """
    # Calculate cost based on input text length (characters)
    char_count = len(text)
    cost = (char_count / 1000) * GEMINI_TTS_PRICING["input_cost_per_1k_chars"]
    return round(cost, 6)  # Round to 6 decimal places for precision

@router.post("/tts")
async def gemini_text_to_speech(
    text: str = Form(...),
    voice_name: VoiceName = Form("Kore"),
    style_instructions: Optional[str] = Form(None),
    user_tenant_info: UserTenantDB = Depends(get_tenant_info)
):
    """
    Generate speech from text using Google Gemini TTS API.
    Uploads the generated audio to MinIO and returns a presigned URL.
    Requires authentication via Bearer token.

    Available voices:
    - Kore (Female, Firm)
    - Puck (Male, Optimistic)
    - Leda (Female, Youthful)
    - Aoede (Male, Fresh)
    """
    logger.info(f"Starting Gemini TTS request for text: {text[:50]}...")

    try:
        # Fetch Gemini API key from tenant DB
        logger.debug("Fetching Gemini API key from tenant DB")
        api_key_doc = user_tenant_info.db.config.find_one({"name": "api-keys"})

        if not api_key_doc or "GOOGLE_API_KEY" not in api_key_doc.get("value", {}):
            logger.error("Google API key not found in tenant configuration")
            raise HTTPException(status_code=500, detail="Google API key not configured")

        api_key = api_key_doc["value"]["GOOGLE_API_KEY"]
        logger.debug(f"API key retrieved (length: {len(api_key)})")

        # Initialize Gemini client
        client = genai.Client(api_key=api_key)
        logger.debug("Gemini client initialized")

        # Prepare the text content with optional style instructions
        text_content = text
        if style_instructions:
            text_content = f"{style_instructions}\n\n{text}"

        # Configure TTS generation
        model = "gemini-2.5-flash-preview-tts"
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(text=text_content),
                ],
            ),
        ]

        generate_content_config = types.GenerateContentConfig(
            temperature=1,
            response_modalities=[
                "audio",
            ],
            speech_config=types.SpeechConfig(
                voice_config=types.VoiceConfig(
                    prebuilt_voice_config=types.PrebuiltVoiceConfig(
                        voice_name=voice_name
                    )
                )
            ),
        )

        logger.info("Generating audio with Gemini TTS")

        # Generate audio and collect all chunks
        audio_chunks = []
        usage_metadata = None

        for chunk in client.models.generate_content_stream(
            model=model,
            contents=contents,
            config=generate_content_config,
        ):
            # Capture usage metadata from the response
            if hasattr(chunk, 'usage_metadata') and chunk.usage_metadata:
                usage_metadata = chunk.usage_metadata
                logger.info(f"Usage metadata captured: {usage_metadata}")

            if (
                chunk.candidates is None
                or chunk.candidates[0].content is None
                or chunk.candidates[0].content.parts is None
            ):
                continue

            if chunk.candidates[0].content.parts[0].inline_data and chunk.candidates[0].content.parts[0].inline_data.data:
                inline_data = chunk.candidates[0].content.parts[0].inline_data
                data_buffer = inline_data.data

                # Convert to WAV if needed
                if inline_data.mime_type and not inline_data.mime_type.startswith('audio/wav'):
                    data_buffer = convert_to_wav(inline_data.data, inline_data.mime_type)

                audio_chunks.append(data_buffer)

        if not audio_chunks:
            raise HTTPException(status_code=500, detail="No audio generated from Gemini TTS")

        # Combine all audio chunks
        audio_bytes = b''.join(audio_chunks)
        logger.info(f"Generated audio: {len(audio_bytes)} bytes")

        # Generate unique file ID and name
        file_id = str(uuid.uuid4())
        file_name = f"gemini_tts_{file_id}.wav"

        # Save to MinIO
        logger.info("Saving audio file to MinIO")
        minio_client = user_tenant_info.minio_client
        bucket_name = user_tenant_info.minio_bucket_name

        # Upload to MinIO
        audio_buffer = BytesIO(audio_bytes)
        minio_path = f"gemini_tts/{file_name}"
        minio_client.put_object(
            bucket_name=bucket_name,
            object_name=minio_path,
            data=audio_buffer,
            length=len(audio_bytes),
            content_type="audio/wav"
        )
        logger.info(f"Audio uploaded to MinIO: {minio_path}")

        # Generate presigned URL
        presigned_url = minio_client.presigned_get_object(
            bucket_name=bucket_name,
            object_name=minio_path,
            expires=timedelta(hours=24)
        )
        logger.info("Presigned URL generated successfully")

        # Calculate usage metadata and cost
        prompt_tokens = getattr(usage_metadata, 'prompt_token_count', 0) if usage_metadata else 0
        candidates_tokens = getattr(usage_metadata, 'candidates_token_count', 0) if usage_metadata else 0
        total_tokens = getattr(usage_metadata, 'total_token_count', 0) if usage_metadata else 0

        # Calculate estimated cost
        estimated_cost = calculate_tts_cost(text_content, usage_metadata)

        # Create usage metadata response
        usage_metadata_response = UsageMetadata(
            prompt_token_count=prompt_tokens,
            candidates_token_count=candidates_tokens,
            total_token_count=total_tokens,
            estimated_cost_usd=estimated_cost
        )

        # Store metadata in database
        tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)
        tts_record = {
            "file_id": file_id,
            "file_name": file_name,
            "minio_path": minio_path,
            "text": text,
            "voice_name": voice_name,
            "style_instructions": style_instructions,
            "user_id": user_tenant_info.user.id,
            "created_at": datetime.now(timezone.utc),
            "file_size": len(audio_bytes),
            "usage_metadata": {
                "prompt_token_count": prompt_tokens,
                "candidates_token_count": candidates_tokens,
                "total_token_count": total_tokens,
                "estimated_cost_usd": estimated_cost
            }
        }

        await tenant_database.tts.insert_one(tts_record)
        logger.info(f"TTS record saved to database with file_id: {file_id}")

        return GeminiTTSResponse(
            message="Audio generated successfully",
            file_id=file_id,
            file_name=file_name,
            minio_path=minio_path,
            presigned_url=presigned_url,
            text=text,
            voice_name=voice_name,
            created_at=tts_record["created_at"],
            usage_metadata=usage_metadata_response
        )

    except HTTPException:
        # Re-raise HTTPExceptions without modification
        raise
    except Exception as e:
        logger.error(f"Gemini TTS error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

def convert_to_wav(audio_data: bytes, mime_type: str) -> bytes:
    """Generates a WAV file header for the given audio data and parameters.

    Args:
        audio_data: The raw audio data as a bytes object.
        mime_type: Mime type of the audio data.

    Returns:
        A bytes object representing the WAV file header.
    """
    parameters = parse_audio_mime_type(mime_type)
    bits_per_sample = parameters["bits_per_sample"]
    sample_rate = parameters["rate"]
    num_channels = 1
    data_size = len(audio_data)
    bytes_per_sample = bits_per_sample // 8
    block_align = num_channels * bytes_per_sample
    byte_rate = sample_rate * block_align
    chunk_size = 36 + data_size  # 36 bytes for header fields before data chunk size

    # http://soundfile.sapp.org/doc/WaveFormat/

    header = struct.pack(
        "<4sI4s4sIHHIIHH4sI",
        b"RIFF",          # ChunkID
        chunk_size,       # ChunkSize (total file size - 8 bytes)
        b"WAVE",          # Format
        b"fmt ",          # Subchunk1ID
        16,               # Subchunk1Size (16 for PCM)
        1,                # AudioFormat (1 for PCM)
        num_channels,     # NumChannels
        sample_rate,      # SampleRate
        byte_rate,        # ByteRate
        block_align,      # BlockAlign
        bits_per_sample,  # BitsPerSample
        b"data",          # Subchunk2ID
        data_size         # Subchunk2Size (size of audio data)
    )
    return header + audio_data

def parse_audio_mime_type(mime_type: str) -> dict[str, int | None]:
    """Parses bits per sample and rate from an audio MIME type string.

    Assumes bits per sample is encoded like "L16" and rate as "rate=xxxxx".

    Args:
        mime_type: The audio MIME type string (e.g., "audio/L16;rate=24000").

    Returns:
        A dictionary with "bits_per_sample" and "rate" keys. Values will be
        integers if found, otherwise None.
    """
    bits_per_sample = 16
    rate = 24000

    # Extract rate from parameters
    parts = mime_type.split(";")
    for param in parts: # Skip the main type part
        param = param.strip()
        if param.lower().startswith("rate="):
            try:
                rate_str = param.split("=", 1)[1]
                rate = int(rate_str)
            except (ValueError, IndexError):
                # Handle cases like "rate=" with no value or non-integer value
                pass # Keep rate as default
        elif param.startswith("audio/L"):
            try:
                bits_per_sample = int(param.split("L", 1)[1])
            except (ValueError, IndexError):
                pass # Keep bits_per_sample as default if conversion fails

    return {"bits_per_sample": bits_per_sample, "rate": rate}
