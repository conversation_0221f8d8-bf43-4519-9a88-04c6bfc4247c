from fastapi import APIRouter, Depends
from fastapi import UploadFile, File, Form, HTTPException
from fastapi.responses import JSONResponse
import os
from typing import Optional
import logging
import uuid
from datetime import datetime, timezone, timedelta
from io import BytesIO
from bson import ObjectId
from core.security import get_tenant_info
from core.database import get_async_db_from_tenant_id
from models.user import UserTenantDB

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/voice-clone", tags=["Voice Cloning"])

@router.post("/clone")
async def clone_voice(
    user_name: str = Form(...),  # Voice name from request body
    user_id: str = Form(...),
    file: UploadFile = File(...),
    user_tenant_info: UserTenantDB = Depends(get_tenant_info)
):
    """
    Clone a voice by uploading a single audio sample and storing it in MinIO.
    Returns voice_id as MongoDB ObjectId and file information with presigned URLs.
    Requires authentication via Bearer token.
    """
    logger.info(f"Starting voice cloning request for user: {user_id}, voice: {user_name}")

    try:
        # Validate input
        if not file:
            raise HTTPException(status_code=400, detail="Audio file is required")

        # Get MinIO client and bucket info
        logger.info("Getting MinIO client for file storage")
        minio_client = user_tenant_info.minio_client
        bucket_name = user_tenant_info.minio_bucket_name

        # Get database connection
        tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)

        # Generate unique voice_id using MongoDB ObjectId
        voice_id = str(ObjectId())
        logger.info(f"Generated voice_id: {voice_id}")

        # Process and upload audio file
        minio_base_path = f"voice_cloning/{user_id}/{user_name}/"

        # Validate file type
        if not file.content_type or not file.content_type.startswith('audio/'):
            logger.warning(f"File {file.filename} has invalid content type: {file.content_type}")
            # Continue processing but log warning

        # Read file content
        contents = await file.read()
        if len(contents) == 0:
            raise HTTPException(status_code=400, detail=f"File {file.filename} is empty")

        # Generate unique filename for the audio file
        file_id = str(uuid.uuid4())
        file_extension = os.path.splitext(file.filename)[1] if file.filename else '.mp3'
        file_name = f"voice_sample_1_{file_id}{file_extension}"

        # Create MinIO path
        minio_path = f"{minio_base_path}{file_name}"

        # Upload to MinIO
        logger.info(f"Uploading file to MinIO: {minio_path}")
        audio_buffer = BytesIO(contents)
        minio_client.put_object(
            bucket_name=bucket_name,
            object_name=minio_path,
            data=audio_buffer,
            length=len(contents),
            content_type=file.content_type or "audio/mp3"
        )

        # Generate presigned URL for direct access (valid for 24 hours)
        try:
            presigned_url = minio_client.presigned_get_object(
                bucket_name=bucket_name,
                object_name=minio_path,
                expires=timedelta(hours=24)
            )
            logger.debug(f"Generated presigned URL for {file_name}")
        except Exception as e:
            logger.error(f"Failed to generate presigned URL for {file_name}: {str(e)}")
            presigned_url = f"Error generating URL: {str(e)}"

        saved_files = [{
            "file_name": file_name,
            "minio_path": minio_path,
            "file_size": len(contents),
            "presigned_url": presigned_url
        }]

        logger.debug(f"Saved audio file: {minio_path}")

        # Store voice metadata in database
        logger.info("Storing cloned voice metadata in cloned_voices collection")
        voice_doc = {
            "_id": ObjectId(voice_id),  # Use the generated voice_id as document _id
            "voice_id": voice_id,
            "user_id": user_id,
            "voice_name": user_name,
            "username": user_tenant_info.user.username,
            "created_at": datetime.now(timezone.utc),
            "audio_files": saved_files,
            "total_files": len(saved_files),
            "minio_base_path": minio_base_path,
            "file_type": "voice_cloning"
        }

        await tenant_database.cloned_voices.insert_one(voice_doc)
        logger.info(f"Voice metadata stored with voice_id: {voice_id}")

        # Return response in ElevenLabs format
        response_data = {
            "voice_id": voice_id,
            "message": f"Voice '{user_name}' cloned successfully",
            "user_id": user_id,
            "voice_name": user_name,
            "files_saved": len(saved_files),
            "minio_base_path": minio_base_path,
            "audio_files": saved_files
        }

        logger.info("Voice cloning process completed successfully")
        return JSONResponse(content=response_data, status_code=200)

    except HTTPException:
        raise  # Let FastAPI handle HTTPExceptions as is
    except Exception as e:
        logger.error(f"Unexpected error during voice cloning: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Voice cloning failed: {str(e)}")


@router.get("/list-voices")
async def list_voices(
    user_id: Optional[str] = None,
    user_tenant_info: UserTenantDB = Depends(get_tenant_info)
):
    """
    List all cloned voices with voice clone ID and voice clone name.
    Requires authentication via Bearer token.
    """
    logger.info("Starting request to list voices")

    try:
        # Get database connection
        tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)

        # Build query filter
        query_filter = {"file_type": "voice_cloning"}
        if user_id:
            query_filter["user_id"] = user_id

        # Fetch voices from database
        voices_cursor = tenant_database.cloned_voices.find(query_filter)
        voices_list = await voices_cursor.to_list(length=None)

        # Format response - simple list with just voice_id and voice_name
        formatted_voices = []
        for voice in voices_list:
            formatted_voices.append({
                "voice_clone_id": str(voice["_id"]),
                "voice_clone_name": voice.get("voice_name", "Unknown")
            })

        logger.info(f"Found {len(formatted_voices)} voices")
        return JSONResponse(
            content=formatted_voices,
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error listing voices: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list voices: {str(e)}")


@router.get("/voice/{voice_id}")
async def get_voice_details(
    voice_id: str,
    user_tenant_info: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get detailed information about a specific voice including audio files.
    Requires authentication via Bearer token.
    """
    logger.info(f"Getting details for voice_id: {voice_id}")

    try:
        # Validate voice_id format
        try:
            ObjectId(voice_id)
        except Exception:
            raise HTTPException(status_code=400, detail="Invalid voice_id format")

        # Get database connection
        tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)

        # Find voice by voice_id
        voice = await tenant_database.cloned_voices.find_one({"_id": ObjectId(voice_id)})

        if not voice:
            raise HTTPException(status_code=404, detail="Voice not found")

        # Return detailed voice information
        response_data = {
            "voice_id": str(voice["_id"]),
            "voice_name": voice.get("voice_name", "Unknown"),
            "user_id": voice.get("user_id", "Unknown"),
            "username": voice.get("username", "Unknown"),
            "created_at": voice.get("created_at").isoformat() if voice.get("created_at") else None,
            "files_saved": voice.get("total_files", 0),
            "minio_base_path": voice.get("minio_base_path", ""),
            "audio_files": voice.get("audio_files", [])
        }

        logger.info(f"Voice details retrieved for voice_id: {voice_id}")
        return JSONResponse(content=response_data, status_code=200)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting voice details: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get voice details: {str(e)}")
    