from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
from datetime import datetime, timezone, timedelta
from typing import Optional, List, Dict, Any
import logging

# Custom imports
from core.security import get_tenant_info
from core.database import get_async_db_from_tenant_id
from models.user import UserTenantDB

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/cost-tracking", tags=["Cost Tracking"])

class CostSummary(BaseModel):
    total_cost_usd: float
    total_requests: int
    total_tokens: int
    average_cost_per_request: float
    date_range: Dict[str, str]

class ServiceCostBreakdown(BaseModel):
    service_name: str
    total_cost_usd: float
    total_requests: int
    total_tokens: int
    average_cost_per_request: float

class UserCostSummary(BaseModel):
    user_id: str
    username: str
    total_cost_usd: float
    total_requests: int
    services: List[ServiceCostBreakdown]

class CostTrackingResponse(BaseModel):
    message: str
    summary: CostSummary
    service_breakdown: List[ServiceCostBreakdown]
    user_breakdown: List[UserCostSummary]

class DetailedUsageRecord(BaseModel):
    record_id: str
    service: str
    user_id: str
    username: str
    created_at: datetime
    text_length: int
    usage_metadata: Dict[str, Any]
    cost_usd: float

class DetailedUsageResponse(BaseModel):
    message: str
    total_records: int
    records: List[DetailedUsageRecord]
    pagination: Dict[str, Any]

@router.get("/summary")
async def get_cost_summary(
    start_date: Optional[str] = Query(None, description="Start date in YYYY-MM-DD format"),
    end_date: Optional[str] = Query(None, description="End date in YYYY-MM-DD format"),
    user_id: Optional[str] = Query(None, description="Filter by specific user ID"),
    service: Optional[str] = Query(None, description="Filter by service (tts, inference, etc.)"),
    user_tenant_info: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get cost summary and breakdown for TTS and other services.
    Requires authentication via Bearer token.
    """
    logger.info(f"Cost summary requested by user: {user_tenant_info.user.username}")

    try:
        # Parse date range
        if start_date:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        else:
            start_dt = datetime.now(timezone.utc) - timedelta(days=30)  # Default to last 30 days

        if end_date:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(tzinfo=timezone.utc) + timedelta(days=1)
        else:
            end_dt = datetime.now(timezone.utc)

        # Build query filter
        query_filter = {
            "created_at": {"$gte": start_dt, "$lt": end_dt}
        }

        if user_id:
            query_filter["user_id"] = user_id

        # Get database connection
        tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)

        # Query TTS records
        tts_records = []
        if not service or service == "tts":
            async for record in tenant_database.tts.find(query_filter):
                tts_records.append(record)

        # Query inference records (if they exist)
        inference_records = []
        if not service or service == "inference":
            try:
                async for record in tenant_database.cloned_voices.find(query_filter):
                    if "usage_metadata" in record:
                        inference_records.append(record)
            except Exception as e:
                logger.warning(f"Could not query inference records: {e}")

        # Calculate summary statistics
        total_cost = 0.0
        total_requests = 0
        total_tokens = 0
        service_stats = {}
        user_stats = {}

        # Process TTS records
        for record in tts_records:
            cost = record.get("usage_metadata", {}).get("estimated_cost_usd", 0.0)
            tokens = record.get("usage_metadata", {}).get("total_token_count", 0)
            user_id = record.get("user_id", "unknown")
            username = record.get("username", "unknown")

            total_cost += cost
            total_requests += 1
            total_tokens += tokens

            # Service breakdown
            if "tts" not in service_stats:
                service_stats["tts"] = {"cost": 0.0, "requests": 0, "tokens": 0}
            service_stats["tts"]["cost"] += cost
            service_stats["tts"]["requests"] += 1
            service_stats["tts"]["tokens"] += tokens

            # User breakdown
            if user_id not in user_stats:
                user_stats[user_id] = {
                    "username": username,
                    "cost": 0.0,
                    "requests": 0,
                    "services": {"tts": {"cost": 0.0, "requests": 0, "tokens": 0}}
                }
            user_stats[user_id]["cost"] += cost
            user_stats[user_id]["requests"] += 1
            if "tts" not in user_stats[user_id]["services"]:
                user_stats[user_id]["services"]["tts"] = {"cost": 0.0, "requests": 0, "tokens": 0}
            user_stats[user_id]["services"]["tts"]["cost"] += cost
            user_stats[user_id]["services"]["tts"]["requests"] += 1
            user_stats[user_id]["services"]["tts"]["tokens"] += tokens

        # Process inference records
        for record in inference_records:
            cost = record.get("usage_metadata", {}).get("estimated_cost_usd", 0.0)
            tokens = record.get("usage_metadata", {}).get("total_token_count", 0)
            user_id = record.get("user_id", "unknown")
            username = record.get("username", "unknown")

            total_cost += cost
            total_requests += 1
            total_tokens += tokens

            # Service breakdown
            if "inference" not in service_stats:
                service_stats["inference"] = {"cost": 0.0, "requests": 0, "tokens": 0}
            service_stats["inference"]["cost"] += cost
            service_stats["inference"]["requests"] += 1
            service_stats["inference"]["tokens"] += tokens

            # User breakdown
            if user_id not in user_stats:
                user_stats[user_id] = {
                    "username": username,
                    "cost": 0.0,
                    "requests": 0,
                    "services": {}
                }
            user_stats[user_id]["cost"] += cost
            user_stats[user_id]["requests"] += 1
            if "inference" not in user_stats[user_id]["services"]:
                user_stats[user_id]["services"]["inference"] = {"cost": 0.0, "requests": 0, "tokens": 0}
            user_stats[user_id]["services"]["inference"]["cost"] += cost
            user_stats[user_id]["services"]["inference"]["requests"] += 1
            user_stats[user_id]["services"]["inference"]["tokens"] += tokens

        # Build response
        summary = CostSummary(
            total_cost_usd=round(total_cost, 6),
            total_requests=total_requests,
            total_tokens=total_tokens,
            average_cost_per_request=round(total_cost / total_requests, 6) if total_requests > 0 else 0.0,
            date_range={
                "start": start_dt.strftime("%Y-%m-%d"),
                "end": (end_dt - timedelta(days=1)).strftime("%Y-%m-%d")
            }
        )

        service_breakdown = []
        for service_name, stats in service_stats.items():
            service_breakdown.append(ServiceCostBreakdown(
                service_name=service_name,
                total_cost_usd=round(stats["cost"], 6),
                total_requests=stats["requests"],
                total_tokens=stats["tokens"],
                average_cost_per_request=round(stats["cost"] / stats["requests"], 6) if stats["requests"] > 0 else 0.0
            ))

        user_breakdown = []
        for user_id, stats in user_stats.items():
            user_services = []
            for service_name, service_stats in stats["services"].items():
                user_services.append(ServiceCostBreakdown(
                    service_name=service_name,
                    total_cost_usd=round(service_stats["cost"], 6),
                    total_requests=service_stats["requests"],
                    total_tokens=service_stats["tokens"],
                    average_cost_per_request=round(service_stats["cost"] / service_stats["requests"], 6) if service_stats["requests"] > 0 else 0.0
                ))

            user_breakdown.append(UserCostSummary(
                user_id=user_id,
                username=stats["username"],
                total_cost_usd=round(stats["cost"], 6),
                total_requests=stats["requests"],
                services=user_services
            ))

        logger.info(f"Cost summary generated: ${total_cost:.6f} across {total_requests} requests")

        return CostTrackingResponse(
            message="Cost summary generated successfully",
            summary=summary,
            service_breakdown=service_breakdown,
            user_breakdown=user_breakdown
        )

    except ValueError as e:
        logger.error(f"Invalid date format: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Invalid date format. Use YYYY-MM-DD: {str(e)}")
    except Exception as e:
        logger.error(f"Cost tracking error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.get("/detailed-usage")
async def get_detailed_usage(
    start_date: Optional[str] = Query(None, description="Start date in YYYY-MM-DD format"),
    end_date: Optional[str] = Query(None, description="End date in YYYY-MM-DD format"),
    user_id: Optional[str] = Query(None, description="Filter by specific user ID"),
    service: Optional[str] = Query(None, description="Filter by service (tts, inference, etc.)"),
    page: int = Query(1, ge=1, description="Page number (starts from 1)"),
    limit: int = Query(50, ge=1, le=1000, description="Number of records per page"),
    user_tenant_info: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get detailed usage records with pagination.
    Requires authentication via Bearer token.
    """
    logger.info(f"Detailed usage requested by user: {user_tenant_info.user.username}")

    try:
        # Parse date range
        if start_date:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        else:
            start_dt = datetime.now(timezone.utc) - timedelta(days=7)  # Default to last 7 days

        if end_date:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(tzinfo=timezone.utc) + timedelta(days=1)
        else:
            end_dt = datetime.now(timezone.utc)

        # Build query filter
        query_filter = {
            "created_at": {"$gte": start_dt, "$lt": end_dt}
        }

        if user_id:
            query_filter["user_id"] = user_id

        # Get database connection
        tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)

        # Calculate pagination
        skip = (page - 1) * limit

        # Collect all records
        all_records = []

        # Query TTS records
        if not service or service == "tts":
            async for record in tenant_database.tts.find(query_filter).sort("created_at", -1):
                all_records.append({
                    "record_id": str(record.get("_id", "")),
                    "service": "tts",
                    "user_id": record.get("user_id", "unknown"),
                    "username": record.get("username", "unknown"),
                    "created_at": record.get("created_at"),
                    "text_length": len(record.get("text", "")),
                    "usage_metadata": record.get("usage_metadata", {}),
                    "cost_usd": record.get("usage_metadata", {}).get("estimated_cost_usd", 0.0),
                    "file_name": record.get("file_name", ""),
                    "voice_name": record.get("voice_name", "")
                })

        # Query inference records
        if not service or service == "inference":
            try:
                async for record in tenant_database.cloned_voices.find(query_filter).sort("created_at", -1):
                    if "usage_metadata" in record:
                        all_records.append({
                            "record_id": str(record.get("_id", "")),
                            "service": "inference",
                            "user_id": record.get("user_id", "unknown"),
                            "username": record.get("username", "unknown"),
                            "created_at": record.get("created_at"),
                            "text_length": len(record.get("generation_text", "")),
                            "usage_metadata": record.get("usage_metadata", {}),
                            "cost_usd": record.get("usage_metadata", {}).get("estimated_cost_usd", 0.0),
                            "voice_id": record.get("voice_id", "")
                        })
            except Exception as e:
                logger.warning(f"Could not query inference records: {e}")

        # Sort by created_at descending
        all_records.sort(key=lambda x: x["created_at"] or datetime.min.replace(tzinfo=timezone.utc), reverse=True)

        # Apply pagination
        total_records = len(all_records)
        paginated_records = all_records[skip:skip + limit]

        # Convert to response format
        detailed_records = []
        for record in paginated_records:
            detailed_records.append(DetailedUsageRecord(
                record_id=record["record_id"],
                service=record["service"],
                user_id=record["user_id"],
                username=record["username"],
                created_at=record["created_at"] or datetime.now(timezone.utc),
                text_length=record["text_length"],
                usage_metadata=record["usage_metadata"],
                cost_usd=record["cost_usd"]
            ))

        # Pagination info
        total_pages = (total_records + limit - 1) // limit
        pagination = {
            "current_page": page,
            "total_pages": total_pages,
            "total_records": total_records,
            "records_per_page": limit,
            "has_next": page < total_pages,
            "has_previous": page > 1
        }

        logger.info(f"Detailed usage generated: {len(detailed_records)} records on page {page}")

        return DetailedUsageResponse(
            message="Detailed usage records retrieved successfully",
            total_records=total_records,
            records=detailed_records,
            pagination=pagination
        )

    except ValueError as e:
        logger.error(f"Invalid date format: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Invalid date format. Use YYYY-MM-DD: {str(e)}")
    except Exception as e:
        logger.error(f"Detailed usage error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )
