from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict
import logging

# Custom imports
from core.security import get_tenant_info
from core.database import get_async_db_from_tenant_id
from models.user import UserTenantDB

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/cost-tracking", tags=["Cost Tracking"])

class CostSummary(BaseModel):
    total_cost_usd: float
    total_cost_npr: float
    total_requests: int
    total_tokens: int
    average_cost_per_request_usd: float
    average_cost_per_request_npr: float
    date_range: Dict[str, str]

class CostTrackingResponse(BaseModel):
    message: str
    summary: CostSummary



@router.get("/summary")
async def get_cost_summary(
    start_date: Optional[str] = Query(None, description="Start date in YYYY-MM-DD format"),
    end_date: Optional[str] = Query(None, description="End date in YYYY-MM-DD format"),
    user_tenant_info: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get cost summary and breakdown for TTS and other services.
    Requires authentication via Bearer token.
    """
    logger.info(f"Cost summary requested by user: {user_tenant_info.user.username}")

    try:
        # Parse date range
        if start_date:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        else:
            start_dt = datetime.now(timezone.utc) - timedelta(days=30)  # Default to last 30 days

        if end_date:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(tzinfo=timezone.utc) + timedelta(days=1)
        else:
            end_dt = datetime.now(timezone.utc)

        # Build query filter
        query_filter = {
            "created_at": {"$gte": start_dt, "$lt": end_dt}
        }

        # Get database connection
        tenant_database = get_async_db_from_tenant_id(user_tenant_info.tenant_id)

        # Query TTS records only
        tts_records = []
        async for record in tenant_database.tts.find(query_filter):
            tts_records.append(record)

        # Calculate summary statistics
        total_cost_usd = 0.0
        total_cost_npr = 0.0
        total_requests = 0
        total_tokens = 0

        # Process TTS records
        for record in tts_records:
            cost_usd = record.get("usage_metadata", {}).get("estimated_cost_usd", 0.0)
            cost_npr = record.get("usage_metadata", {}).get("estimated_cost_npr", 0.0)
            tokens = record.get("usage_metadata", {}).get("total_token_count", 0)

            total_cost_usd += cost_usd
            total_cost_npr += cost_npr
            total_requests += 1
            total_tokens += tokens

        # Build response
        summary = CostSummary(
            total_cost_usd=round(total_cost_usd, 6),
            total_cost_npr=round(total_cost_npr, 4),
            total_requests=total_requests,
            total_tokens=total_tokens,
            average_cost_per_request_usd=round(total_cost_usd / total_requests, 6) if total_requests > 0 else 0.0,
            average_cost_per_request_npr=round(total_cost_npr / total_requests, 4) if total_requests > 0 else 0.0,
            date_range={
                "start": start_dt.strftime("%Y-%m-%d"),
                "end": (end_dt - timedelta(days=1)).strftime("%Y-%m-%d")
            }
        )

        logger.info(f"Cost summary generated: ${total_cost_usd:.6f} USD / Rs.{total_cost_npr:.4f} NPR across {total_requests} requests")

        return CostTrackingResponse(
            message="Cost summary generated successfully",
            summary=summary
        )

    except ValueError as e:
        logger.error(f"Invalid date format: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Invalid date format. Use YYYY-MM-DD: {str(e)}")
    except Exception as e:
        logger.error(f"Cost tracking error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


